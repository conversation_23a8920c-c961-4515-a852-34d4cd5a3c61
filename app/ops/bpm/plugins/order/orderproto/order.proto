syntax = "proto3";
package app.ops.bpm.plugins.order.orderproto;

import "aixpublic/server/api/msgcommon/common.proto";


// @category(工单)

// 金三云服务器申请
// @POST(/order/server/ks/apply)
message KsServerApplyReq {
  // 通用头
  // @required
  msgcommon.Common common = 1;
  string region = 2;
  string zone = 3 ;
  string image = 4 ;
  // 硬件规格ID
  // @required
  string hardware_spec = 5;   
  // 硬件规格描述
  // @required    
  string hardware_spec_detail = 6;  
  string sys_disk_type = 7;
  string data_disk_type = 8;
  int64 data_disk_size = 9;
  // 申请理由
  string apply_msg = 10;
  // 收费模式
  // @required
  string charge_type = 11;  
  string instance_name = 12;      
  string business_tree_branch = 13;
  string proposer_email = 14;
  string ops_owner_email = 15;
  // 是否加急，0为否，1为是 
  // @required
  int32 exigency = 16;    
}

message KsServerApplyResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
  string order_id = 2;
}

// 获取已完结工单
// @POST(/order/my-done)
message GetMyDoneOrderReq {
  // 通用头
  // @required
  msgcommon.Common common = 1;
  int64 page = 2;
  int64 page_size = 3;
  // 迭代三再实现：按角色筛选
  repeated string filter_by_role = 10;
}

message GetMyDoneOrderResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
  int32 total = 2;
  repeated Order orders = 3;
}

message Order{
  string order_id = 1;
  string order_type = 2;
  string order_type_name = 3;
  int32 total_stage_num = 4;
  int32 current_stage = 5;
  string current_stage_name = 6;
  string stage_operator = 7;
  string proposer_email = 8;
  string ops_owner_email = 9;
  string apply_msg = 10;
  string info = 11;
  int32 result = 12;
  string result_desc = 13;
  string result_msg = 14;
  string apply_datetime = 15;
  string last_updated_datetime = 16;
  // 迭代二再实现：用户角色类型
  string role_type = 20;
}

// 获取进行中的工单
// @POST(/order/my-doing)
message GetMyDoingOrderReq {
  // 通用头
  // @required
  msgcommon.Common common = 1;
  int64 page = 2;
  int64 page_size = 3;
  // 迭代三再实现：按角色筛选
  repeated string filter_by_role = 10;
}

message GetMyDoingOrderResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
  int32 total = 2;
  repeated Order orders = 3;
}

// 获取我的审批工单
// @POST(/order/my-audit)
message GetMyAuditOrderReq {
  // 通用头
  // @required
  msgcommon.Common common = 1;
  int64 page = 2;
  int64 page_size = 3;
  // 工单号（模糊搜索）
  string order_id = 4;
  // 工单标题（模糊搜索）
  string title = 5;
  // 申请开始时间（ISO 8601格式）
  string applied_start_date = 6;
  // 申请结束时间（ISO 8601格式）
  string applied_end_date = 7;
  // 工单类型数组
  repeated string order_types = 8;
  // 迭代三再实现：按角色筛选
  repeated string filter_by_role = 10;
}

message GetMyAuditOrderResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
  int32 total = 2;
  repeated Order orders = 3;
  // 用户拥有的所有工单类型
  repeated string order_types = 4;
}

// 获取工单详情
// @POST(/order/detail)
message GetOrderDetailReq {
  // 通用头
  // @required
  msgcommon.Common common = 1;
  string order_id = 2;
}

message GetOrderDetailResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
  Order order_info = 2;
  repeated Stage stage_infos = 3;
}

message Stage{
  int32 stage_num = 1;
  string stage_name = 2;
  string stage_type = 3;
  string stage_operator = 4;
  int32 stage_result = 5;
  string stage_result_desc = 6;
  string stage_result_msg = 7;
  string apply_datetime = 8;
  string last_updated_datetime = 9;
}

// 服务器跳板机账号授权
// @POST(/order/server/jump/impower)
message ServerJumpImpowerReq {
  // @required
  msgcommon.Common common = 1;

  // ip地址
  // @required
  repeated string server_ip = 2;

  // 权限类型 1 为普通权限，2 为root权限
  // @required
  int32 impower_type = 3;

  // 权限持续时长，单位：天
  // @required
  int32 day_num = 4;

  // 申请理由
  string apply_msg = 5;

  // 是否加急，0为否，1为是 
  // @required
  int32 exigency = 6; 
}

message ServerJumpImpowerResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
  // 返回内容
  // @required
  int32 result = 2;
}


// 华为cdn加速域名创建
// @POST(/cdn/domain/create)
message CdnDomainCreateReq {
  // @required
  msgcommon.Common common = 1;

  // 加速域名
  // @required
  string domain_name = 2;

  // 域名业务类型 若为web，则表示类型为网页加速；若为download，则表示业务类型为文件下载加速；若为video，则表示业务类型为点播加速；若为wholeSite，则表示业务类型为全站加速
  // @required
  string business_type = 3;

  // 源站域名或源站IP
  // @required
  string ip_or_domain = 4;

  // 域名服务范围 mainland_china，则表示服务范围为中国大陆；若为outside_mainland_china，则表示服务范围为中国大陆境外；若为global，则表示服务范围为全球
  // @required
  string domain_service_area = 5;

  // 源站类型取值：ipaddr、 domain、obs_bucket，分别表示：源站IP、源站域名、OBS桶访问域名
  // @required
  string origin_type = 6;

  // 运维审批人
  // @required
  string ops_audit_email = 7;

  // 申请理由
  // @required
  string apply_msg = 8;

}

message CdnDomainCreateResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
  // 返回内容
  // @required
  int32 result = 2;
}


// 腾讯云域名解析
// @POST(/domain/resolve)
message DomainResolveReq {
  // @required
  msgcommon.Common common = 1;


  // 域名
  // @required
  string domain = 2;

  // 域名记录类型，大写英文，比如：A CNAME MX
  // @required
  string record_type = 3;

  //记录值，如 IP : ***************， CNAME : cname.dnspod.com.， MX : mail.dnspod.com.
  // @required
  string value = 4 ;

  // 运维审批人
  // @required
  string ops_audit_email = 5;

  // 申请理由
  // @required
  string apply_msg = 6;

}

message DomainResolveResp {
    // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
  // 返回内容
  // @required
  int32 result = 2;
} 

// 附件
message Attachment {
  string name = 1;
  string path = 2;
}

// 抄送用户信息
message CcUserInfo {
  string cc_open_id = 1; // 抄送人飞书Open ID
  string cc_email = 2;   // 抄送人邮箱
}

// 用户校验请求
// @POST(/order/validate-user)
message ValidateUserReq {
  // 通用头
  // @required
  msgcommon.Common common = 1;
  string email = 2; // 待校验的邮箱
}

// 用户校验响应
message ValidateUserResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
  string name = 2;       // 员工姓名
  string department = 3; // 所属部门
  string open_id = 4;    // 飞书Open ID
}

// 通用工单
// @POST(/order/common)
message CommonOrderReq {
  // @required
  msgcommon.Common common = 1;

  // 工单类型
  // @required
  string order_type = 2;

  // 表单json数据
  // @required
  string apply_info = 3;

  // 表单json数据
  // @required
  string apply_msg = 4;

  // 表单json数据
  // @required
  repeated Attachment attachment_info = 5;

  // 是否加急，0为否，1为是
  // @required
  int32 exigency = 6;

  // 标题
  // @required
  string title = 7;

  // 抄送人信息列表
  repeated CcUserInfo cc_user_infos = 10;

}

message CommonOrderResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
  // 返回内容
  // @required
  int32 result = 2;
}

// 页面审批
// @POST(/order/approval)
message OrderApprovalReq {
  // @required
  msgcommon.Common common = 1;

  // 工单ID
  // @required
  string order_id = 2;

  // 工单阶段数
  // @required
  int32 stage_num = 3;

  // 审批结果，e.g yes 通过，no 拒绝
  // @required
  string chosen_action = 4;
}

message OrderApprovalResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
}

// 审批人变更
// @POST(/order/flow-audit/turn)
message TurnFlowAuditReq {
  // @required
  msgcommon.Common common = 1;

  // 工单ID
  // @required
  string order_id = 2;

  // 工单阶段数
  // @required
  int32 stage_num = 3;

  // 审批结果，e.g yes 通过，no 拒绝
  // @required
  string new_operator_email = 4;
}

message TurnFlowAuditResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
}

// 模型节点新增工单
// @POST(/order/model/new-node)
message NewModelNodeReq {
  // 申请信息
  // @required
  string apply_msg = 1;
  // 节点层级
  // @required
  int32 node_layer = 2;
  // 节点名称
  // @required
  string node_name = 3;
  // 业务负责人
  // @required
  string business_owner = 4;
  // 审批负责人
  string audit_owner = 5;
}
message NewModelNodeResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
}

// 模型子节点新增工单
// @POST(/order/model/new-child-node)
message NewModelChildNodeReq {
  // 申请信息
  // @required
  string apply_msg = 1;
  // 父节点id
  // @required
  int32 parent_id = 2;
  // 节点名称
  // @required
  string node_name = 3;
  // 产品负责人
  string product_owner = 4;
  // 开发负责人
  string develop_owner = 5;
  // 审批负责人
  string audit_owner = 6;
}
message NewModelChildNodeResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
}

// 模型节点修改工单
// @POST(/order/model/modify-node)
message ModifyModelNodeReq {
  // 申请信息
  // @required
  string apply_msg = 1;
  // 节点层级
  // @required
  int32 node_id = 2;
  // 节点名称
  // @required
  string node_name = 3;
  // 业务负责人
  // @required
  string business_owner = 4;
  // 产品负责人
  // @required
  string product_owner = 5;
  // 开发负责人
  // @required
  string develop_owner = 6;
  // 审批负责人
  string audit_owner = 7;
}
message ModifyModelNodeResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
}

// 模型节点删除工单
// @POST(/order/model/delete-node)
message DeleteModelNodeReq {
  // 申请信息
  // @required
  string apply_msg = 1;
  // 节点层级
  // @required
  int32 node_id = 2;
  // 审批负责人
  string audit_owner = 3;
}
message DeleteModelNodeResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
}

// 模型节点移动工单
// @POST(/order/model/drag-node)
message DragModelNodeReq {
  // 申请信息
  // @required
  string apply_msg = 1;
  // 旧节点ID
  // @required
  int32 node_id = 2;
  // 新父节点ID
  // @required
  int32 new_parent_node_id = 3;
  // 审批负责人
  string audit_owner = 4;
}
message DragModelNodeResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
}

message ModelNodePath{
  int32 parent_id = 1;
  int32 child_id = 2;
  int32 weight = 3;
  int32 auto_weight = 4;
}
// 成本模型分摊设置工单
// @POST(/order/model/cost/apportion-conf)
message CostModelApportionConfReq {
  // 申请信息
  // @required
  string apply_msg = 1;
  // 路径信息
  // @required
  repeated ModelNodePath path_list = 2;
}
message CostModelApportionConfResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
}


// k8s token 申请
// @POST(/order/token/apply)
message TokenApplyReq {
  // @required
  msgcommon.Common common = 1;
  // @required
  string cluster  = 2;
  // @required
  string token_type = 3;
  // @required
  string ns= 4;
  // @required
  string name= 5;
  // 挂载节点id
  // @required
  int32 cost_model_node_id = 6;
  // @required
  string ops_audit_email = 7;
  // @required
  string apply_msg = 8;
  // @required
  string path = 9;
  // @required
  string proposer_email = 10;
}

message TokenApplyResp {
  // @required
  msgcommon.RespCommon resp_common = 1;
  // 返回内容
  // @required
  int32 result = 2;
  
}


// 云资源销毁
// @POST(/order/resouce/delete)
message ResouceDeleteReq {
  // @required
  msgcommon.Common common = 1;
  // @required
  int32 supplier  = 2;
  // @required
  string region = 3;
  // @required
  string resource_type= 4;
  // @required
  string resource_id= 5;
  // @required
  string resource_name= 6;
  // @required
  string apply_msg = 7;
  // @required
  string ops_audit_email = 8;
}

message ResouceDeleteResp {
  // @required
  msgcommon.RespCommon resp_common = 1;
  // 返回内容
  // @required
  int32 result = 2;
  
}

// 购买域名工单
// @POST(/order/domain/buy)
message DomainOrderReq {
  // @required
  msgcommon.Common common = 1;

  // 域名
  // @required
  string domain = 2;

  // 域名持有人
  string whois = 3;

  // 描述
  // @required
  string describe = 4;

  // 购买时常
  // @required
  int32 buy_time = 5;

  // 业务树
  // @required
  string business_tree_branch = 6;

  // 运维负责人
  string ops_owner = 8;
}

message DomainOrderResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
  // 返回内容
  // @required
  int32 result = 2;
}



// 云服务器购买工单
// @POST(/order/cloud/server/buy)
message BuyCloudServerReq {
  // 地区
  // @required
  string supplier = 1;

  // 地区
  // @required
  string region_id = 2;

  // 可用区
  // @required
  string zone_id = 3;

  // 实例规格
  // @required
  string instance_type_id = 4;

  // 系统盘
  // @required
  Disk sys_disk = 5;

  // 数据盘
  // @required
  repeated Disk data_disk = 6;

  // 数据盘镜像
  // @required
  string image_id  = 7;

  // 实例名称
  // @required
  string instance_name = 8;

  // 密码
  // @required
  string password = 9;

  // 是否测试
  // @required
  bool dry_run = 10;

  // 申请理由
  // @required
  string apply_msg = 11;

  // 挂载节点id
  // @required
  int32 cost_model_node_id = 12;

  // 运维审批人
  // @required
  string ops_audit_email = 13;

  string proposer_email = 14;
}

message Disk{
  int32 size_gb = 1;
	string category = 2;
}
message BuyCloudServerResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
}


message MysqlVolume {
  string category = 1;
  int32 size_gb = 2 ;
}

message MysqlInstanceType {
  string instance_type_group = 1;
  string Instance_type_id = 2;
  int32 cpu = 3;
  int32 memory_gb = 4;
}

// 云 Mysql 购买工单
// @POST(/order/cloud/mysql/buy)
message BuyCloudMysqlReq {
  // 是否测试
  // @required
  bool dry_run = 1;

    // 运维审批人
  // @required
  string ops_audit_email = 2;

  // 申请理由
  // @required
  string apply_msg = 3;

  // 挂载节点id
  // @required
  int32 cost_model_node_id = 4;

  // 供应山
  // @required
  string supplier = 5;

  // 地区
  // @required
  string region_id = 6;

  // 可用区
  // @required
  string zone_id = 7;

  // 实例规格
  // @required
  string instance_type_id = 8;

  // 实例名称
  // @required
  string instance_name = 9;

  // 硬盘
  // @required
  MysqlVolume volume = 10;

  // 版本
  // @required
  string version = 11;

  // 副本数
  // @required
  int32 replica_num = 12;

  // 副本可用区1
  // @required
  string backup_one_zone_id = 13;

  // 副本可用区2
  // @required
  string backup_two_zone_id = 14;

  // 是否大小写铭感
  bool ignore_case = 15;

  // 规格
  MysqlInstanceType instance_type = 16;

  string proposer_email = 17;
}

message BuyCloudMysqlResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
}

// 云Redis购买工单
// @POST(/order/cloud/redis/buy)
message BuyCloudRedisReq {
  // 是否测试
  // @required
  bool dry_run = 1;

  // 运维审批人
  // @required
  string ops_audit_email = 2;

  // 申请人邮箱
  string proposer_email = 3;

  // 申请理由
  // @required
  string apply_msg = 4;

  // 挂载节点id
  // @required
  int32 cost_model_node_id = 5;

  // 供应山
  // @required
  string supplier = 6;

  // 地区
  // @required
  string region_id = 7;

  // 可用区
  // @required
  string zone_id = 8;

  // 实例规格
  // @required
  string instance_type_id = 9;

  // 实例名称
  // @required
  string instance_name = 10;

  // 版本
  // @required
  string version = 11;

  // 副本数
  // @required
  int32 replica_num = 12;

  // 副本可用区
  // @required
  string backup_zone_id = 13;

  // 切片数量
  int32 shard_num = 14;

  // 内存
  float memory_gb = 15;
}

message BuyCloudRedisResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
}


// 更新资源服务负责人
// @POST(/order/cmdb/resource/update-service-owner)
message UpdateCMDBResourceServiceOwnerReq {
  // @required
  msgcommon.Common common = 1;

  string new_service_owner = 2;

  string old_service_owner = 3;

  repeated string ids = 4;

  string apply_msg = 5;

  string old_service_owner_leader_audit_email = 6;

  string new_service_owner_leader_audit_email = 7;

  string ops_audit_email = 8;
}

message UpdateCMDBResourceServiceOwnerResp {
  // 通用返回头
  // @required
  msgcommon.RespCommon resp_common = 1;
}
