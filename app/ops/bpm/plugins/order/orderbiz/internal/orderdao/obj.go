package orderdao

import (
	"time"
)

type SelectCount struct {
	TotalNum int64 `json:"total_num" yaml:"total_num" db:"total_num"`
}

type TbCurrentOrderStageInfo struct {
	OrderID       string    `json:"order_id" yaml:"order_id" db:"order_id"`
	OrderType     string    `json:"order_type" yaml:"order_type" db:"order_type"`
	TotalStageNum int64     `json:"total_stage_num" yaml:"total_stage_num" db:"total_stage_num"`
	CurrentStage  int64     `json:"current_stage" yaml:"current_stage" db:"current_stage"`
	StageName     string    `json:"stage_name" yaml:"stage_name" db:"stage_name"`
	Operator      string    `json:"operator" yaml:"operator" db:"operator"`
	ProposerEmail string    `json:"proposer_email" yaml:"proposer_email" db:"proposer_email"`
	OpsOwnerEmail string    `json:"ops_owner_email" yaml:"ops_owner_email" db:"ops_owner_email"`
	Result        int64     `json:"result" yaml:"result" db:"result"`
	Ctime         time.Time `json:"ctime" yaml:"ctime" db:"ctime"`
	Mtime         time.Time `json:"mtime" yaml:"mtime" db:"mtime"`
}

// MyAuditOrderSearchParam 我的审批工单搜索参数
// 用于支持工单列表的动态查询功能，包含分页、筛选和搜索条件
type MyAuditOrderSearchParam struct {
	PageNum          int      `json:"page_num"`           // 页码，从1开始
	PageSize         int      `json:"page_size"`          // 每页数量
	OrderId          string   `json:"order_id"`           // 工单号（模糊搜索）
	Title            string   `json:"title"`              // 工单标题（模糊搜索，在info的JSON字段中搜索）
	AppliedStartDate *string  `json:"applied_start_date"` // 申请开始时间（ISO 8601格式）
	AppliedEndDate   *string  `json:"applied_end_date"`   // 申请结束时间（ISO 8601格式）
	OrderTypes       []string `json:"order_types"`        // 工单类型数组
	OperatorEmail    string   `json:"operator_email"`     // 审批人邮箱（必填）
}

// TbOrderExtended 扩展的工单结构体
type TbOrderExtended struct {
	Id            int       `json:"id" yaml:"id" db:"id"`
	OrderId       string    `json:"order_id" yaml:"order_id" db:"order_id"`
	OrderType     string    `json:"order_type" yaml:"order_type" db:"order_type"`
	Info          string    `json:"info" yaml:"info" db:"info"`
	Exigency      int       `json:"exigency" yaml:"exigency" db:"exigency"`
	ApplyMsg      string    `json:"apply_msg" yaml:"apply_msg" db:"apply_msg"`
	ProposerEmail string    `json:"proposer_email" yaml:"proposer_email" db:"proposer_email"`
	OpsOwnerEmail string    `json:"ops_owner_email" yaml:"ops_owner_email" db:"ops_owner_email"`
	TotalStageNum int       `json:"total_stage_num" yaml:"total_stage_num" db:"total_stage_num"`
	CurrentStage  int       `json:"current_stage" yaml:"current_stage" db:"current_stage"`
	Result        int       `json:"result" yaml:"result" db:"result"`
	ResultMsg     string    `json:"result_msg" yaml:"result_msg" db:"result_msg"`
	IsDel         int       `json:"is_del" yaml:"is_del" db:"is_del"`
	Ctime         time.Time `json:"ctime" yaml:"ctime" db:"ctime"`
	Mtime         time.Time `json:"mtime" yaml:"mtime" db:"mtime"`
}

// OrderCc 对应数据库中的 tb_order_cc 表
type OrderCc struct {
	ID       int64     `gorm:"column:id;primaryKey;autoIncrement"`
	OrderID  string    `gorm:"column:order_id"`
	CcOpenID string    `gorm:"column:cc_open_id"`
	CcEmail  string    `gorm:"column:cc_email"`
	Ctime    time.Time `gorm:"column:ctime;autoCreateTime"`
	Mtime    time.Time `gorm:"column:mtime;autoUpdateTime"`
}

func (m *OrderCc) TableName() string {
	return "tb_order_cc"
}

// GetMyOrdersDAOParam 用于列表查询的参数结构体（为迭代三预留）
type GetMyOrdersDAOParam struct {
	UserID       string   `json:"user_id"`
	Page         int      `json:"page"`
	PageSize     int      `json:"page_size"`
	FilterByRole []string `json:"filter_by_role"` // 迭代三再实现：按角色筛选
}

// OrderWithRoleInfo 连表查询结果结构体，包含工单信息和角色信息
type OrderWithRoleInfo struct {
	// 工单基本信息
	Id            int       `db:"id"`
	OrderId       string    `db:"order_id"`
	OrderType     string    `db:"order_type"`
	Info          string    `db:"info"`
	Exigency      int       `db:"exigency"`
	ApplyMsg      string    `db:"apply_msg"`
	ProposerEmail string    `db:"proposer_email"`
	OpsOwnerEmail string    `db:"ops_owner_email"`
	TotalStageNum int       `db:"total_stage_num"`
	CurrentStage  int       `db:"current_stage"`
	Result        int       `db:"result"`
	ResultMsg     string    `db:"result_msg"`
	IsDel         int       `db:"is_del"`
	Ctime         time.Time `db:"ctime"`
	Mtime         time.Time `db:"mtime"`

	// 角色信息
	IsApplicant       bool `db:"is_applicant"`        // 是否为申请人
	IsToBeApproved    bool `db:"is_to_be_approved"`   // 是否为待审批人
	IsAlreadyApproved bool `db:"is_already_approved"` // 是否为已审批人
	IsCcToMe          bool `db:"is_cc_to_me"`         // 是否为抄送人
}
