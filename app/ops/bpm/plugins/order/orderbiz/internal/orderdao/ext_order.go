package orderdao

import (
	"context"
	"fmt"
	"strings"
	"time"

	"aix.zhhainiao.com/aixpublic/server/core/ctxlog"
	"aix.zhhainiao.com/opspublic/utils/aixdao"
	"github.com/jmoiron/sqlx"
)

// getMyAuditOrderWhereSQL 构建动态查询的WHERE条件和参数
func getMyAuditOrderWhereSQL(ctx context.Context, param *MyAuditOrderSearchParam) (whereSQL string, args []interface{}) {
	filter := []string{}

	// 必须条件：审批人邮箱
	filter = append(filter, `tb_order.order_id IN (
		SELECT DISTINCT(order_id) 
		FROM tb_stage
		WHERE operator = ?
	)`)
	args = append(args, param.OperatorEmail)

	// 处理工单号模糊搜索
	if param.OrderId != "" {
		filter = append(filter, "tb_order.order_id LIKE CONCAT('%',?,'%')")
		args = append(args, param.OrderId)
	}

	// 处理标题模糊搜索（在title、apply_msg字段以及info的JSON中title字段中搜索）
	if param.Title != "" {
		// 对LIKE查询中的特殊字符进行转义，避免 SQL LIKE 特殊字符干扰
		escapedTitle := strings.ReplaceAll(param.Title, "\\", "\\\\")
		escapedTitle = strings.ReplaceAll(escapedTitle, "%", "\\%")
		escapedTitle = strings.ReplaceAll(escapedTitle, "_", "\\_")

		sqlFragment := `(
			JSON_VALID(tb_order.info) = 1 AND 
			JSON_UNQUOTE(JSON_EXTRACT(tb_order.info, '$.title')) IS NOT NULL AND
			JSON_UNQUOTE(JSON_EXTRACT(tb_order.info, '$.title')) LIKE CONCAT('%',?,'%')
		)`
		filter = append(filter, sqlFragment)
		args = append(args, escapedTitle)
	}

	// 处理申请开始时间（前端直接传MySQL格式：YYYY-MM-DD HH:MM:SS）
	if param.AppliedStartDate != nil && *param.AppliedStartDate != "" {
		filter = append(filter, "tb_order.ctime >= ?")
		args = append(args, *param.AppliedStartDate)
	}

	// 处理申请结束时间（前端直接传MySQL格式：YYYY-MM-DD HH:MM:SS）
	if param.AppliedEndDate != nil && *param.AppliedEndDate != "" {
		filter = append(filter, "tb_order.ctime <= ?")
		args = append(args, *param.AppliedEndDate)
	}

	// 处理工单类型数组筛选
	if len(param.OrderTypes) > 0 {
		filter = append(filter, "tb_order.order_type IN (?)")
		args = append(args, param.OrderTypes)
	}

	// 处理删除标志
	filter = append(filter, "tb_order.is_del = 0")

	whereSQL = strings.Join(filter, " AND ")
	return
}

// SelectMyAuditOrders 查询我的审批工单列表（带动态条件）
func (dao *OrderDao) SelectMyAuditOrders(ctx context.Context, param *MyAuditOrderSearchParam) (
	orderList []TbOrderExtended, total int32, err error) {

	tx := dao.OrderMysql.getDb()

	// SQL模板，%s分别为: 目标字段、WHERE条件、排序、分页
	templateSQL := `
		SELECT 
			%s
		FROM
			tb_order
		WHERE
			%s
		%s
		%s
	`

	var whereSQL string
	var orderSQL string

	// 处理目标 sql 片段
	queryTarget := `tb_order.id, tb_order.order_id, tb_order.order_type, tb_order.info, 
		tb_order.exigency, tb_order.apply_msg, tb_order.proposer_email, tb_order.ops_owner_email,
		tb_order.total_stage_num, tb_order.current_stage, tb_order.result, tb_order.result_msg,
		tb_order.is_del, tb_order.ctime, tb_order.mtime`
	countTarget := "COUNT(*) AS total"

	// 处理where sql 片段
	whereSQL, baseArgs := getMyAuditOrderWhereSQL(ctx, param)

	// 处理排序 sql 片段
	orderSQL = "ORDER BY result ASC, ctime DESC"

	// 查询总数（使用基础搜索条件，不包含分页参数）
	countSQL := fmt.Sprintf(templateSQL, countTarget, whereSQL, "", "")
	realCountSQL, countArgs, err := sqlx.In(countSQL, baseArgs...)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("sqlx.In count err=%s,sql:%s,args:%#v", err, countSQL, baseArgs)
		return
	}

	// 为计数查询设置较短的超时时间
	countCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	totalInt, err := dao.Count(countCtx, tx, realCountSQL, countArgs)
	if err != nil {
		if err == context.DeadlineExceeded {
			ctxlog.WithCtx(ctx).Errorf("Count query timeout after 30s")
		} else {
			ctxlog.WithCtx(ctx).Errorf("dao.Count err=%s", err)
		}
		return
	}
	total = int32(totalInt)

	// 查询数据（使用分页查询函数）
	querySQL := fmt.Sprintf(templateSQL, queryTarget, whereSQL, orderSQL, "%s")
	orderList, err = PagedQuery[[]TbOrderExtended](ctx, tx, querySQL, baseArgs, param.PageNum, param.PageSize)
	if err != nil {
		return
	}

	return
}

// PagedQuery 分页查询的公共方法（泛型版本）
// T 必须是切片类型，如 []SomeStruct
func PagedQuery[T any](ctx context.Context, tx interface{}, querySQL string, baseArgs []interface{}, pageNum, pageSize int) (
	resultList T, err error) {
	// 处理分页参数
	queryArgs := make([]interface{}, len(baseArgs))
	copy(queryArgs, baseArgs) // 复制基础搜索条件参数

	var pageSQL string
	if pageSize > 0 {
		pageSQL = "LIMIT ?,?"
		offset := (pageNum - 1) * pageSize
		limit := pageSize
		queryArgs = append(queryArgs, offset, limit)
	}

	// 构建完整的SQL语句
	realQuerySQL := strings.Replace(querySQL, "%s", pageSQL, 1)
	finalSQL, finalArgs, err := sqlx.In(realQuerySQL, queryArgs...)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("sqlx.In query err=%s,sql:%s,args:%#v", err, realQuerySQL, queryArgs)
		return
	}

	// 为数据查询设置超时时间
	queryCtx, cancel := context.WithTimeout(ctx, 45*time.Second)
	defer cancel()

	// 根据传入的tx类型进行类型断言并执行查询
	if queryer, ok := tx.(sqlx.QueryerContext); ok {
		err = sqlx.SelectContext(queryCtx, queryer, &resultList, finalSQL, finalArgs...)
	} else {
		err = fmt.Errorf("invalid tx type")
		return
	}

	if err != nil {
		if err == context.DeadlineExceeded {
			ctxlog.WithCtx(ctx).Errorf("PagedQuery timeout after 45s")
		} else {
			ctxlog.WithCtx(ctx).Errorf("PagedQuery err=%s", err)
		}
		return
	}

	return
}

// Count 查询总数的公共方法
func (dao *OrderDao) Count(ctx context.Context, tx interface{}, countSQL string, args []interface{}) (total int, err error) {
	countResource := make([]aixdao.SelectCountResult, 0)

	// 根据传入的tx类型进行类型断言
	if queryer, ok := tx.(sqlx.QueryerContext); ok {
		err = sqlx.SelectContext(ctx, queryer, &countResource, countSQL, args...)
	} else {
		err = fmt.Errorf("invalid tx type")
		return
	}

	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("Count err=%+v,sqlTotal=%s", err, countSQL)
		return
	}
	if len(countResource) > 0 {
		total = countResource[0].Total
	} else {
		err = fmt.Errorf("ext sql count zero err")
		return
	}
	return
}

// GetMyOrdersWithRole 根据用户ID和角色查询相关工单列表（迭代二、三再实现）
// 注意：此函数明确禁止使用JOIN操作，将采用"应用层聚合"模式
func (dao *OrderDao) GetMyOrdersWithRole(ctx context.Context, param *GetMyOrdersDAOParam) (orderList []TbOrderExtended, total int32, err error) {
	// TODO: 迭代二、三再实现
	// 未来实现逻辑:
	// 1. 构建SQL，使用UNION ALL合并三个查询结果:
	//    - SELECT ... FROM tb_order WHERE proposer_email = ? (申请人)
	//    - SELECT ... FROM tb_order JOIN tb_stage WHERE operator = ? (审批人)
	//    - SELECT ... FROM tb_order JOIN tb_order_cc WHERE cc_open_id = ? (抄送人)
	// 2. 使用 CASE WHEN ... THEN ... END 来动态生成 role_type 字段
	// 3. 如果 param.FilterByRole 非空，则在最外层查询添加 WHERE role_type IN (...)
	// 4. 严禁使用JOIN，采用应用层聚合模式

	ctxlog.WithCtx(ctx).Infof("GetMyOrdersWithRole placeholder called with param: %+v", param)

	// 当前迭代返回空结果
	orderList = []TbOrderExtended{}
	total = 0
	return
}
