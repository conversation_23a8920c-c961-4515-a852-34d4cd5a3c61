package orderdao

import (
	"context"
	"fmt"
	"strings"

	"aix.zhhainiao.com/aixpublic/server/core/ctxlog"
	"github.com/jmoiron/sqlx"
)

// BatchNewCcRecords 批量插入抄送记录
func (d *OrderDao) BatchNewCcRecords(ctx context.Context, records []*OrderCc) error {
	if len(records) == 0 {
		return nil
	}

	// 构建批量插入SQL
	db := d.OrderMysql.getDb()

	// 构建VALUES子句
	valueStrings := make([]string, 0, len(records))
	args := make([]interface{}, 0, len(records)*3) // 每条记录3个字段（不包括自增ID和时间戳）

	for _, record := range records {
		valueStrings = append(valueStrings, "(?, ?, ?)")
		args = append(args, record.OrderID, record.CcOpenID, record.CcEmail)
	}

	sql := fmt.Sprintf(`
		INSERT INTO tb_order_cc (order_id, cc_open_id, cc_email)
		VALUES %s
	`, strings.Join(valueStrings, ","))

	// 执行批量插入
	_, err := db.ExecContext(ctx, sql, args...)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("BatchNewCcRecords failed: %v", err)
		return err
	}

	ctxlog.WithCtx(ctx).Infof("BatchNewCcRecords success, inserted %d records", len(records))
	return nil
}

// GetOrderIDsByCcOpenID 根据被抄送人的open_id，查询所有相关的order_id
func (d *OrderDao) GetOrderIDsByCcOpenID(ctx context.Context, ccOpenID string) ([]string, error) {
	var orderIDs []string

	tx := d.OrderMysql.getDb()
	sql := "SELECT order_id FROM tb_order_cc WHERE cc_open_id = ?"

	if queryer, ok := tx.(sqlx.QueryerContext); ok {
		err := sqlx.SelectContext(ctx, queryer, &orderIDs, sql, ccOpenID)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("GetOrderIDsByCcOpenID failed: %v", err)
			return nil, err
		}
	} else {
		ctxlog.WithCtx(ctx).Errorf("GetOrderIDsByCcOpenID: tx is not sqlx.QueryerContext")
		return nil, fmt.Errorf("tx is not sqlx.QueryerContext")
	}

	ctxlog.WithCtx(ctx).Infof("GetOrderIDsByCcOpenID success, found %d order IDs for cc_open_id: %s", len(orderIDs), ccOpenID)
	return orderIDs, nil
}
