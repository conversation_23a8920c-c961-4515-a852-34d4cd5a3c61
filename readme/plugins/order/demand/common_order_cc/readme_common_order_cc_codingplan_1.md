# 项目迭代一：工单抄送功能（后端）

#### **总览**

本次迭代旨在实现创建工单时的抄送核心功能，包括后端数据模型、用户校验API以及在创建工单流程中保存抄送关系。我们将严格遵循项目的“Codegen-First”和“DAO-First”开发范式，同时对工具链不支持的批量操作采用手动编写扩展的方式实现。所有步骤都设计为独立、可验证的小步迭代。

#### **最终代码目录结构 (迭代一完成后)**

```
app/ops/bpm/plugins/
└── order/
    ├── order.go                      # [改造] Handler层：新增 ValidateUser 接口；改造 CommonOrder 接口编排抄送逻辑。
    ├── orderdto/
    │   └── biz_objects.go            # [改造] 新增 ValidatedUserInfo, CcUserInfo DTO，为列表查询预留参数对象。
    └── orderbiz/
        ├── order.go                  # [改造] Service层：新增用户校验、抄送记录创建的业务逻辑；为后续功能预留函数签名。
        └── internal/
            └── orderdao/
                ├── def_order_cc.go   # [新增] 【代码源头-占位】定义空的IOrderCcMysqlDao接口，用于维护结构统一性。
                ├── ext_order_cc.go   # [新增] 【手动编写】实现自定义的BatchNewCcRecords批量插入方法。
                ├── ext_order.go      # [改造] 【手动编写】为迭代二、三的动态查询预留函数占位（明确禁止JOIN）。
                ├── obj.go            # [改造] 【手动维护】新增OrderCc结构体；为列表查询预留动态参数结构体。
                └── zz_generated.def_order_cc.go # (无新增) 此文件本次迭代不会生成与`tb_order_cc`相关的内容。
└── orderproto/
    └── order.proto                 # [改造] 【代码源头】新增 ValidateUser API；改造现有API以支持抄送和未来筛选功能。
```

#### **开发流程Mermaid图**

```mermaid
flowchart TD
    subgraph Step 1: 基础结构与API定义
        A[1.1: 创建占位文件 def_order_cc.go] --> B[1.2: 手动定义DAO结构体 obj.go];
        B --> C[1.3: 定义API order.proto 并运行 `make plugin`];
        C --> D[1.4: 创建数据库表 tb_order_cc];
    end
    subgraph " "
        direction LR
        D -- 可验证 --> V1[验证: 数据库表结构正确, 项目编译通过, 服务可正常启动];
    end

    subgraph Step 2: 实现自定义DAO
        E[2.1: 新增 ext_order_cc.go 文件] --> F[2.2: 实现 BatchNewCcRecords 方法];
    end
    subgraph " "
        direction LR
        F -- 可验证 --> V2[验证: 可通过单元测试或临时代码调用, 成功批量写入数据];
    end
    
    subgraph Step 3: 实现用户校验API
        G["3.1 (Service): 实现 ValidateUserAndBuildInfo"] --> H["3.2 (Handler): 实现 ValidateUser"];
    end
    subgraph " "
        direction LR
        H -- 可验证 --> V3[验证: 调用 /order/validate-user 接口, 能正确返回用户信息或错误提示];
    end
    
    subgraph Step 4: 集成核心抄送逻辑
        I["4.1 (Service): 实现 CreateCcRecords] --> J[4.2 (Handler): 改造 CommonOrder 方法"];
    end
    subgraph " "
        direction LR
        J -- 可验证 --> V4[验证: 创建带抄送人的工单, tb_order 和 tb_order_cc 表均写入正确数据];
    end

    subgraph Step 5: 遵循架构原则进行占位
        K[5.1: 改造异步函数签名] --> L["5.2: 为列表查询功能添加占位符(明确禁止JOIN)"];
    end
    subgraph " "
        direction LR
        L -- 可验证 --> V5[验证: 创建工单功能正常, 列表查询API返回结构包含新占位字段且功能无回归性问题];
    end
    
    Step1 --> Step2 --> Step3 --> Step4 --> Step5;
```

---

### **渐进式小步迭代式开发与集成步骤**

#### **第一步：基础结构与API定义 (Foundation & Codegen)**

此步骤为所有后续开发奠定基础，并严格遵循项目结构规范。

1.  **声明DAO接口 (Structural Placeholder)**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/` 目录下，创建新文件 `def_order_cc.go`。此文件仅作为结构占位和文档，**不用于代码生成**。
        ```go
        //go:build gogendao
        // +build gogendao

        package orderdao
        
        /*
        * sql:
        *   CREATE TABLE `tb_order_cc` (
        *     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
        *     `order_id` varchar(16) NOT NULL COMMENT '工单ID',
        *     `cc_open_id` varchar(64) NOT NULL COMMENT '被抄送人飞书Open ID',
        *     `cc_email` varchar(32) NOT NULL COMMENT '被抄送人邮箱',
        *     `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        *     `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
        *     PRIMARY KEY (`id`),
        *     UNIQUE KEY `uk_order_cc` (`order_id`,`cc_open_id`)
        *   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单抄送关系表';
        */
        
        type OrderCc struct{} 
        
        // inject: mysql.default
        type IOrderCcMysqlDao interface {}
        ```
    *   **验证**: 确认文件已创建，项目可以正常编译。

2.  **定义DAO层结构体**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/obj.go` 文件中，**手动添加** `OrderCc` 结构体的完整定义。
        ```go
        package orderdao
        
        import "time"
        
        // OrderCc 对应数据库中的 tb_order_cc 表
        type OrderCc struct {
            ID        int64     `gorm:"column:id;primaryKey;autoIncrement"`
            OrderID   string    `gorm:"column:order_id"`
            CcOpenID  string    `gorm:"column:cc_open_id"`
            CcEmail   string    `gorm:"column:cc_email"`
            Ctime     time.Time `gorm:"column:ctime;autoCreateTime"`
            Mtime     time.Time `gorm:"column:mtime;autoUpdateTime"`
        }
        
        func (m *OrderCc) TableName() string {
            return "tb_order_cc"
        }
        // ...（obj.go 中已有的其他结构体）
        ```
    *   **验证**: 项目可以无错误地编译。

3.  **定义接口协议 (Proto-First)**:
    *   **任务**: 打开 `app/ops/bpm/plugins/order/orderproto/order.proto`，新增 `ValidateUser` 相关API和 `CcUserInfo` 消息体，并改造 `CommonOrderReq`。
    *   **验证**: 运行 `make plugin name=order`，确保项目编译通过，且相关 `*.pb.go` 文件已更新。

4.  **定义DTO**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/orderdto/biz_objects.go` 中，手动新增 `ValidatedUserInfo` 和 `CcUserInfo` 结构体。
    *   **验证**: 确保项目编译通过。

5.  **数据库准备**:
    *   **任务**: 根据 `def_order_cc.go` 文件头部的 `CREATE TABLE` 注释，在数据库中手动创建 `tb_order_cc` 表。
    *   **验证**: 确认数据库表结构、字段类型、主键及唯一索引与设计文档完全一致。

---

#### **第二步：实现自定义数据访问逻辑 (DAO Extension)**

此步骤聚焦于手动编写代码生成器无法覆盖的数据库操作。

1.  **创建DAO扩展文件**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/` 目录下，创建新文件 `ext_order_cc.go`。

2.  **实现批量插入方法**:
    *   **任务**: 在 `ext_order_cc.go` 中，为 `Dao` 结构体添加 `BatchNewCcRecords` 方法。
        ```go
        package orderdao

        import "context"

        // BatchNewCcRecords 批量插入抄送记录
        func (d *Dao) BatchNewCcRecords(ctx context.Context, records []*OrderCc) error {
            if len(records) == 0 {
                return nil
            }
            return d.db.WithContext(ctx).Create(&records).Error
        }
        ```
    *   **验证**: 通过单元测试验证此方法的逻辑正确性。

---

#### **第三步：实现并暴露用户校验API (End-to-End)**

此步骤完成一个独立的、可供前端使用的完整功能。

1.  **实现Service层逻辑**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/orderbiz/order.go` 中，新增 `ValidateUserAndBuildInfo(...)` 方法。
    *   **验证**: 通过单元测试验证此方法的逻辑正确性。

2.  **实现Handler层接口**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/order.go` 中，实现 `ValidateUser(...)` 接口。
    *   **验证**: 启动服务，使用API测试工具（如cURL, Postman）调用 `/order/validate-user` 接口，验证其能正确返回用户信息或错误提示。

---

#### **第四步：在创建工单流程中集成抄送功能**

此步骤将抄送的核心逻辑与现有工单创建流程无缝结合。

1.  **实现Service层逻辑**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/orderbiz/order.go` 中，新增 `CreateCcRecords(...)` 方法，其内部将调用 `dao.BatchNewCcRecords`。
    *   **验证**: 通过单元测试验证此方法的逻辑正确性。

2.  **改造Handler层 `CommonOrder` 方法**:
    *   **任务**: 修改 `app/ops/bpm/plugins/order/order.go` 中的 `CommonOrder` 方法，在主工单创建成功后调用 `s.biz.CreateCcRecords`。
    *   **验证**: 端到端测试。调用 `/order/common` 接口创建一个带有 `cc_user_infos` 字段的工单，检查数据库 `tb_order` 和 `tb_order_cc` 表的数据是否都正确写入。

---

#### **第五步：遵循架构原则进行占位 (Future-Proofing)**

此步骤确保了API和代码结构的前瞻性，为后续迭代铺平道路。

1.  **改造异步流程函数签名**:
    *   **任务**: 修改 `order.go` (Handler) 中 `go s.biz.NewCreateStage(...)` 的调用，以及 `orderbiz/order.go` (Service) 中 `NewCreateStage` 和 `NewSendFeiShuAudit` 的函数签名，以接收并透传抄送人信息。
    *   **验证**: 再次执行第四步的验证，确保添加参数后，创建工单的整体流程依然正常，无功能回归性问题。

2.  **为列表动态查询准备占位**:
    *   **任务**:
        1.  在 `orderdao/obj.go` 中，手动添加 `GetMyOrdersDAOParam` 结构体。
        2.  在 `orderdao/ext_order.go` 中，添加带详细注释的 `GetMyOrdersWithRole` 占位函数，明确未来将采用“应用层聚合”模式，**严禁使用JOIN**。
    *   **验证**: 启动服务，调用任一现有工单列表查询接口。接口应能正常返回数据，证明无功能回归。确认项目可以成功编译。