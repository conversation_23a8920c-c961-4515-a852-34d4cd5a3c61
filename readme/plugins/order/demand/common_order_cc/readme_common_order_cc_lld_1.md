# 后端详细设计 (LLD) - 工单抄送功能 - 迭代一

## 项目结构与总体设计

本次设计严格遵循您项目的分层架构和"Codegen-First，人工扩展为辅"的开发范式。核心改动集中在`Handler`, `Service`, `DAO`层，通过新增和扩展现有模块的方式，将抄送功能无缝集成到工单创建和查询流程中。

设计核心在于：
1.  **同步写入，异步通知**：`Handler`层在编排工单创建时，会同步完成工单主数据和抄送关系的数据库写入，并立即响应客户端以优化体验。耗时的飞书通知操作则通过`goroutine`异步执行。
2.  **职责分离与工程实践对齐**：`Handler`负责流程编排，`Service`负责业务逻辑实现，`DAO`负责数据访问。批量插入的逻辑根据项目规范，被明确地放在手写的`ext_order_cc.go`文件中，而`def_order_cc.go`仅作为结构占位。
3.  **迭代占位**：为确保架构和目录的完整性，与迭代二、三、四相关的功能（如列表查询整合、筛选、消息通知）虽已在结构和函数签名中体现，但其具体实现将使用占位符或固定返回值，在后续迭代中填充。

## 目录结构

```
app/ops/bpm/plugins/
├── order/
│   ├── order.go                      # [改造] Handler层，编排业务逻辑，新增ValidateUser
│   ├── orderdto/
│   │   └── biz_objects.go            # [改造] 新增 DTO，为后续迭代预留结构
│   └── orderbiz/
│       ├── order.go                  # [改造] Service层，实现核心业务逻辑
│       └── internal/
│           └── orderdao/
│               ├── def_order_cc.go   # [新增] 仅作为结构占位和SQL文档，不含代码实现
│               ├── ext_order_cc.go   # [新增] 手动实现自定义的批量插入DAO方法
│               ├── ext_order.go      # [改造] 为迭代2&3预留自定义SQL查询函数占位
│               └── obj.go            # [改造] 新增DAO层数据结构
└── orderproto/
    └── order.proto                 # [改造] 新增API的Protobuf定义
```

## 整体逻辑和交互时序图

下图展示了“创建含抄送人的工单”这一核心场景的完整交互流程。

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant H as "app/ops/bpm/plugins/order/order.go"
    participant S as "app/ops/bpm/plugins/order/orderbiz/order.go"
    participant DAO_EXT as "app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order_cc.go"
    participant FS as "feishuservice.Feishu"

    Note over Client, FS: "前置步骤：校验用户"
    Client->>H: "POST /order/validate-user"
    H->>S: "ValidateUserAndBuildInfo(ctx, req.Email)"
    S->>FS: "GetUserInfoByEmail(ctx, email)"
    FS-->>S: "userInfo, err"
    S->>FS: "GetDepartmentInfo(ctx, userInfo.DepartmentIDS[0])"
    FS-->>S: "departmentInfo, err"
    S-->>H: "validatedUserInfo, err"
    H-->>Client: "HTTP 200 (resp)"

    Note over Client, H: "核心流程：创建工单"
    Client->>H: "POST /order/common (带cc_user_infos)"
    activate H
    H->>S: "NewCreateOrder(ctx, order)"
    note right of H: "沿用现有工单创建逻辑"
    S-->>H: "order, err"
    H->>S: "CreateCcRecords(ctx, order.ID, req.CcUserInfos)"
    activate S
    S->>DAO_EXT: "BatchNewCcRecords(ctx, ccRecords)"
    DAO_EXT-->>S: "err"
    deactivate S
    S-->>H: "err"
    H-->>Client: "HTTP 200 (工单创建成功)"
    
    note over H: "异步启动后续流程"
    H->>S: "go NewCreateStage(ctx, order, req.CcUserInfos)"
    deactivate H
    
    Note over S: "迭代4再实现"
    S->>S: "NewSendFeiShuAudit(ctx, order, req.CcUserInfos)"
```

## API接口定义

-   **`POST /order/validate-user` (新增)**
    -   说明: 校验单个邮箱是否为内部员工，并返回其基本信息。用于前端逐个添加抄送人时的实时校验。
-   **`POST /order/common` (改造)**
    -   说明: 创建通用工单。在现有请求体中增加`cc_user_infos`字段以支持抄送功能。
-   **`POST /GetMyDoingOrder`, `POST /GetMyDoneOrder`, `POST /GetMyAuditOrder` (改造)**
    -   说明: 获取各类工单列表。本次迭代仅为兼容后续筛选功能（迭代3）而改造参数结构，查询逻辑不变。

## 数据实体结构深化

-   **数据表: `tb_order_cc`**

| 字段名 | 类型 | 约束/索引 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | 主键 (PK), 自增 (AI) | 唯一标识符 |
| `order_id` | VARCHAR(16) | 联合唯一索引 (UK with cc_open_id) | 关联的工单ID |
| `cc_open_id` | VARCHAR(64) | 联合唯一索引 (UK with order_id) | 被抄送人的飞书Open ID |
| `cc_email` | VARCHAR(32) | | 被抄送人邮箱（便于排查） |
| `ctime` | DATETIME | | 创建时间 |
| `mtime` | DATETIME | | 更新时间 |

-   **实体关系图 (ER Diagram)**
    *注: `USERS` 为通过飞书API获取的逻辑实体，非本地物理表。*
    ```mermaid
    erDiagram
        USERS {
            string open_id PK "飞书Open ID"
            string email "邮箱"
            string name "姓名"
        }
        ORDERS {
            string order_id PK "工单ID"
            string title "工单标题"
        }
        ORDER_CC {
            bigint id PK
            string order_id FK
            string cc_open_id FK
        }

        ORDERS }|--|{ ORDER_CC : "包含"
        USERS }o--|| ORDER_CC : "被抄送至"
    ```

## 配置项

本次设计不引入任何新的环境变量或配置文件参数。

## 模块化文件详解 (File-by-File Breakdown)

---
### `app/ops/bpm/plugins/orderproto/order.proto`

a. **文件用途说明**
定义API的请求和响应结构。本次新增`ValidateUser`相关消息体，并改造现有工单相关消息体以支持抄送和未来筛选功能。

c. **消息体变更**

#### `ValidateUserReq` (新增)
```protobuf
message ValidateUserReq {
  // ... 现有通用头
  string email = 2; // 待校验的邮箱
}
```

#### `ValidateUserResp` (新增)
```protobuf
message ValidateUserResp {
  // ... 现有通用响应
  string name = 2;       // 员工姓名
  string department = 3; // 所属部门
  string open_id = 4;    // 飞书Open ID
}
```

#### `CcUserInfo` (新增)
```protobuf
message CcUserInfo {
  string cc_open_id = 1; // 抄送人飞书Open ID
  string cc_email = 2;   // 抄送人邮箱
}
```

#### `CommonOrderReq` (改造)
```protobuf
message CommonOrderReq {
  // ... 现有字段
  repeated CcUserInfo cc_user_infos = 10; // 新增：抄送人信息列表
}
```

#### `GetMyDoingOrderReq` etc. (改造)
```protobuf
// 改造 GetMyDoingOrderReq, GetMyDoneOrderReq, GetMyAuditOrderReq 等列表请求
message GetMyDoingOrderReq {
    // ... 现有字段
    repeated string filter_by_role = 10; // 迭代3再实现：按角色筛选
}
```

#### `OrderListItem` in `GetMyDoingOrderResp` etc. (改造)
```protobuf
message OrderListItem {
    // ... 现有字段
    string role_type = 20; // 迭代2再实现：用户角色类型
}
```
---
### `app/ops/bpm/plugins/order/orderdto/biz_objects.go`

a. **文件用途说明**
定义业务逻辑层（Service）内部及与`Handler`层交互时使用的数据传输对象(DTO)。

b. **文件内类图 (`classDiagram`)**
```mermaid
classDiagram
    class ValidatedUserInfo {
        +Name string
        +Department string
        +OpenID string
    }
    class CcUserInfo {
        +CcOpenID string
        +CcEmail string
    }
    class GetMyOrdersParam {
        +FilterByRole []string
        << (迭代3再实现) >>
    }
```

---
### `app/ops/bpm/plugins/order/order.go`

a. **文件用途说明**
API的 Handler 层，负责解析HTTP请求，编排`Service`层方法，并返回响应。

c. **函数/方法详解**

#### **`ValidateUser` (新增)**
- **用途:** 处理`POST /order/validate-user`请求，校验用户邮箱。
- **输入参数:** `ctx context.Context`, `req *orderproto.ValidateUserReq`
- **输出数据结构:** `*orderproto.ValidateUserResp`, `error`
- **实现流程:**
  ```mermaid
  sequenceDiagram
      participant H as "Handler.ValidateUser"
      participant S as "orderbiz.Service"
      H->>S: "ValidateUserAndBuildInfo(ctx, req.Email)"
      S-->>H: "validatedUserInfo, err"
      alt "err is not nil"
          H-->>A_Caller: "返回错误响应"
      else
          H-->>A_Caller: "返回成功响应 (ValidateUserResp)"
      end
  ```

#### **`CommonOrder` (改造)**
- **用途:** 处理`POST /order/common`请求，创建工单并保存抄送人。
- **输入参数:** `ctx context.Context`, `req *orderproto.CommonOrderReq`
- **输出数据结构:** `*orderproto.CommonOrderResp`, `error`
- **实现流程:**
  ```mermaid
  sequenceDiagram
      participant H as "Handler.CommonOrder"
      participant S as "orderbiz.Service"
      
      H->>S: "NewCreateOrder(ctx, orderData)"
      S-->>H: "order, err"
      alt "err is not nil"
        H-->>A_Caller: "返回错误响应"
      end

      alt "req.CcUserInfos is not empty"
        H->>S: "CreateCcRecords(ctx, order.ID, req.CcUserInfos)"
        S-->>H: "err"
        alt "err is not nil"
          note over H: "记录错误日志，但不中断主流程"
        end
      end
      
      H-->>A_Caller: "返回工单创建成功响应"
      
      note over H: "异步启动后续流程"
      H->>S: "go NewCreateStage(ctx, order, req.CcUserInfos)"
  ```
---
### `app/ops/bpm/plugins/order/orderbiz/order.go`

a. **文件用途说明**
业务逻辑（Service）层，实现所有与工单相关的核心业务功能。

c. **函数/方法详解**

#### **`ValidateUserAndBuildInfo` (新增)**
- **用途:** 根据邮箱查询飞书用户，并组装其姓名、部门和OpenID信息。
- **输入参数:** `ctx context.Context`, `email string`
- **输出数据结构:** `*orderdto.ValidatedUserInfo`, `error`
- **实现流程:**
  ```mermaid
  sequenceDiagram
      participant S as "Service.ValidateUserAndBuildInfo"
      participant FS as "feishuservice.Feishu"
      
      S->>FS: "GetUserInfoByEmail(ctx, email)"
      FS-->>S: "userInfo, err"
      alt "err or userInfo is nil"
        S-->>A_Caller: "返回 '用户不存在' 错误"
      end

      S->>FS: "GetDepartmentInfo(ctx, userInfo.DepartmentIDS[0])"
      FS-->>S: "departmentInfo, err"
      alt "err is not nil"
        S-->>A_Caller: "返回 '获取部门信息失败' 错误"
      end

      S-->>A_Caller: "返回组装好的 ValidatedUserInfo"
  ```

#### **`CreateCcRecords` (新增)**
- **用途:** 将抄送人信息批量写入数据库。
- **输入参数:** `ctx context.Context`, `orderID string`, `ccUserInfos []*orderdto.CcUserInfo`
- **输出数据结构:** `error`
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start"] --> B{"遍历 ccUserInfos"};
      B --> C["将每个 CcUserInfo 转换为 orderdao.OrderCc 对象"];
      C --> D["添加到列表中"];
      B -- "遍历完成" --> E["调用 dao.BatchNewCcRecords(ctx, recordList)"];
      E --> F{"检查err"};
      F -- "err != nil" --> G["返回错误"];
      F -- "err == nil" --> H["End"];
  ```

#### **`NewSendFeiShuAudit` (改造)**
- **用途:** **(迭代4再实现)** 向审批人和抄送人发送飞书通知。
- **输入参数:** `ctx context.Context`, `order *Order`, `ccUserInfos []*orderdto.CcUserInfo`
- **输出数据结构:** `error`
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start"] --> B["发送通知给审批人 (沿用现有逻辑)"];
      B --> C{"// 迭代4再实现: 向抄送人发通知"};
      C --> D["用固定返回值或空实现占位"];
      D --> E["End"];
  ```

#### **列表查询函数 (改造)**
- **例如 `GetDoingOrdersByPage`**
- **用途:** **(迭代2&3再实现)** 获取进行中的工单列表。
- **输入参数:** `ctx context.Context`, `param *orderdto.GetMyOrdersParam`
- **输出数据结构:** `List<Order>`, `total`, `error`
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start"] --> B["// 迭代2&3再实现: 按角色查询"];
      B --> C["调用 orderdao.GetMyOrdersWithRole(param)"];
      C --> D["当前迭代: 调用现有DAO查询函数，忽略param"];
      D --> E["End"]
  ```

---
### `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/def_order_cc.go`

a. **文件用途说明**
**结构占位与文档文件**。此文件仅用于定义`tb_order_cc`的建表语句，并提供一个空的DAO接口以维护项目结构统一性，**不包含任何方法实现**，也不会被代码生成器处理。

c. **文件内容**
```go
//go:build gogendao
// +build gogendao

package orderdao

/*
* sql:
*   CREATE TABLE `tb_order_cc` (
*     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
*     `order_id` varchar(16) NOT NULL COMMENT '工单ID',
*     `cc_open_id` varchar(64) NOT NULL COMMENT '被抄送人飞书Open ID',
*     `cc_email` varchar(32) NOT NULL COMMENT '被抄送人邮箱',
*     `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
*     `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
*     PRIMARY KEY (`id`),
*     UNIQUE KEY `uk_order_cc` (`order_id`,`cc_open_id`)
*   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单抄送关系表';
*/

type OrderCc struct{} 

// inject: mysql.default
type IOrderCcMysqlDao interface {}
```

---
### `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order_cc.go` (新增)

a. **文件用途说明**
**手动编写的DAO扩展文件**。所有代码生成器无法处理的、与`tb_order_cc`相关的数据库操作都在此实现。

c. **函数/方法详解**

#### **`BatchNewCcRecords` (新增)**
- **用途:** 向`tb_order_cc`表批量插入多条记录。
- **输入参数:** `ctx context.Context`, `records []*obj.OrderCc`
- **输出数据结构:** `error`
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start"] --> B{"检查 records 是否为空"};
      B -- "是" --> C["直接返回 nil"];
      B -- "否" --> D["使用 d.db.WithContext(ctx).Create(&records)"];
      D --> E["返回GORM操作的error"];
      C --> F["End"];
      E --> F;
  ```

---
### `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/obj.go`

a. **文件用途说明**
定义DAO层与数据库表映射的结构体。

b. **文件内类图 (`classDiagram`)**
```mermaid
classDiagram
    class OrderCc {
        << (table: tb_order_cc) >>
        +ID int64
        +OrderID string
        +CcOpenID string
        +CcEmail string
        +Ctime time.Time
        +Mtime time.Time
    }
    class GetMyOrdersDAOParam {
      +UserID string
      +Page int
      +PageSize int
      +FilterByRole []string
       << (迭代3再实现) >>
    }
```
---
### `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order.go`

a. **文件用途说明**
存放手写的、与`tb_order`表相关的复杂自定义SQL查询。

c. **函数/方法详解**

#### **`GetMyOrdersWithRole` (占位)**
- **用途:** **(迭代2&3再实现)**，根据用户ID和角色查询相关工单列表。
- **输入参数:** `ctx context.Context`, `param *obj.GetMyOrdersDAOParam`
- **输出数据结构:** `List<Order>`, `error`
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start"] --> B["// 迭代2&3再实现，用固定返回值占位"];
      B --> C["未来实现逻辑:"];
      C --> D["明确禁止使用JOIN，将采用应用层聚合"];
      D --> E["返回空列表和 nil error"];
      E --> F["End"];
  ```

---
## 迭代演进依据
这份详细设计确保了系统能够平滑地进行后续迭代：
1.  **架构完整性与项目规范对齐**: 目录结构和文件职责（特别是`def_`和`ext_`的分离）完全符合您的项目开发范式，使得新代码能无缝融入现有体系。
2.  **接口前瞻性**: API请求和DTO结构已包含未来迭代所需的`filter_by_role`和`role_type`字段。这使得前后端可以并行开发，后端在迭代1中仅需忽略这些字段，而在后续迭代中直接启用即可，无需再次修改接口定义。
3.  **逻辑解耦**: 将抄送记录的写入(`CreateCcRecords`)和通知(`NewSendFeiShuAudit`中的异步逻辑)分离，使得每个功能模块职责单一，易于独立开发、测试和维护。
4.  **性能考量路径清晰**: 对于列表查询，我们预留了在`ext_order.go`中进行扩展的路径。根据您的`coding plan`，未来将采用**应用层聚合**而非`JOIN`，这一策略已被明确记录在占位函数中，为未来的性能优化指明了方向。