# 工单系统抄送功能 - 迭代开发规划 (最终版)

本文档定义了工单系统抄送功能的完整需求与分步实现计划。所有功能点已拆分为独立的、可验证的迭代模块。后续开发以此文档为唯一依据。

## 迭代一 (MVP): 核心抄送数据结构与创建功能

### 目标
实现工单抄送功能的核心后端能力和最基础的前端交互。本次迭代完成后，系统能够在创建工单时，校验并保存抄送人信息。

### 后端需求

1.  **数据表结构定义**
    *   **操作:** 创建新表。
    *   **表名:** `tb_order_cc`
    *   **表结构:**
        *   `id` (BIGINT, 主键, 自增)
        *   `order_id` (VARCHAR(16), 工单ID)
        *   `cc_open_id` (VARCHAR(64), 被抄送人飞书Open ID)
        *   `cc_email` (VARCHAR(32), 被抄送人邮箱)
        *   `ctime` (DATETIME, 创建时间)
        *   `mtime` (DATETIME, 更新时间)
    *   **索引:**
        *   为 `order_id` 和 `cc_open_id` 字段创建联合唯一索引 (UNIQUE KEY)，以优化查询并防止重复记录。

2.  **新增API: 邮箱校验接口**
    *   **路径:** `/order/validate-user`
    *   **方法:** `POST`
    *   **请求体结构:**
        *   `common` (msgcommon.Common, required): 通用头。
        *   `email` (string, required): 待校验的邮箱地址。
    *   **成功响应 (Success Response):**
        *   **逻辑:** 在内部员工库中精确匹配邮箱。
        *   **返回体:**
            ```json
            {
              "resp_common": {
                "ret": 0,
                "msg": "ok",
                "request_id": "uuid-xxxx-xxxx"
              },
              "name": "员工姓名",
              "department": "所属部门",
              "open_id": "ou_xxxxxxxxxxxxxxxxxxxx"
            }
            ```
    *   **失败响应 (Failure Response):**
        *   **逻辑:** 邮箱不存在或不属于内部员工。错误信息通过 `resp_common` 字段返回。
        *   **返回体:**
            ```json
            {
              "resp_common": {
                "ret": 1001,
                "msg": "该邮箱不存在或非内部员工，请检查",
                "request_id": "uuid-yyyy-yyyy"
              }
            }
            ```

3.  **改造API: 创建工单接口**
    *   **路径:** (沿用现有创建工单接口)
    *   **方法:** (沿用现有方法)
    *   **请求体变更:** 在现有请求体JSON结构中，增加一个新字段。
        *   **新增字段名:** `cc_user_infos`
        *   **字段类型:** `Array<Object>`
        *   **对象结构:** `{ "cc_open_id": "string", "cc_email": "string" }`
        *   **示例:** `"cc_user_infos": [{"cc_open_id": "ou_xxx1", "cc_email": "<EMAIL>"}, {"cc_open_id": "ou_xxx2", "cc_email": "<EMAIL>"}]`
    *   **处理逻辑:**
        *   接收到 `cc_user_infos` 后，在工单主信息创建成功后，遍历该数组。
        *   将每一条记录中的 `order_id`、`cc_open_id`、`cc_email` 写入 `tb_order_cc` 表。
        *   抄送关系写入失败不应影响工单创建主流程，但需要记录详细错误日志。

### 前端需求

1.  **工单创建页UI布局**
    *   在表单区域，添加一个链接式按钮，文案为 `[添加抄送]`。
    *   默认隐藏抄送配置区。点击该按钮后，展开一个独立的配置区域。

2.  **抄送配置区UI实现**
    *   **输入框:** 类型为 `text`，`placeholder` 提示文案为 `请输入抄送人邮箱，按回车或空格确认`。
    *   **人数计数器:** 位于输入框右侧，灰色文字，格式为 `x/50`，`x` 为当前已添加人数，实时更新。
    *   **错误提示区域:** 在输入框正下方，与“已选列表区”之间，应有一个专门用于显示校验提示信息的区域。当无提示时，此区域不占用垂直空间；当显示提示时，此区域平滑展开以容纳文字，将下方的“已选列表区”平滑地向下推移，避免内容重叠。
    *   **已选列表区:** 位于错误提示区域下方，用于展示已成功添加的抄送人标签。

3.  **核心交互逻辑实现**
    *   **内部状态管理:** 组件内部维护一个状态数组，用于存储已添加的抄送人信息。数组元素为对象格式：`{ open_id: '...', email: '...', display_text: '姓名 (部门)' }`。
    *   **事件触发:** 监听输入框的回车键或空格键，触发校验流程。
    *   **前端自校验:** 校验前，按顺序执行以下检查：
        1.  **检查是否为当前用户:** 若输入邮箱与当前登录用户邮箱相同，触发错误状态。
            *   **反馈:** 输入框触发一次短暂的抖动动画。在下方的错误提示区域显示红色文字：“不能抄送给自己”。
        2.  **检查是否已在列表中:** 若输入邮箱已存在于已选列表中，触发错误状态。
            *   **反馈:** 输入框触发一次短暂的抖动动画。在下方的错误提示区域显示红色文字：“该用户已被添加”。
    *   **错误状态处理:**
        *   当触发任一前端自校验错误时，不清空输入框内容，校验流程终止。
        *   错误提示信息显示3秒后，或在用户下一次输入时，自动平滑消失。
    *   **后端校验调用:** 若所有前端自校验通过，调用 `POST /order/validate-user` 接口。
        *   **失败 (`resp_common.ret !== 0`):** 输入框边框变红，并在错误提示区域显示 `resp_common.msg` 中的错误信息，3秒后自动消失。
        *   **成功 (`resp_common.ret === 0`):** 清空输入框，在“已选列表区”新增标签，更新计数器。若人数达上限，禁用输入框。
    *   **移除与提交:** 标签上的关闭按钮可移除对应人员。提交工单时，从内部状态数组中提取信息，构造成 `cc_user_infos` 字段要求的格式，随表单提交。

### 验收标准
1.  后端已成功部署 `tb_order_cc` 表，其字段、类型、索引均符合定义。
2.  `/order/validate-user` 接口能按照 `RespCommon` 规范返回成功或失败的响应。
3.  创建工单时，`cc_user_infos` 字段能被接口正确接收，并在 `tb_order_cc` 表中查到包含正确 `cc_open_id` 和 `cc_email` 的记录。
4.  当试图添加自己或重复的抄送人时，输入框有抖动效果，并在下方出现对应的文字提示（“不能抄送给自己”或“该用户已被添加”）。
5.  前端在提交表单时，能构造出正确的 `cc_user_infos` 数组对象。

---

## 迭代二: 工单列表整合展示

### 目标
改造所有相关工单列表接口，使抄送给用户的工单能够被查询到，并在前端列表中进行身份标识的统一展示。

### 后端需求

1.  **改造API: “进行中”、“已完结”、“我的审批”工单列表接口**
    *   **返回体变更:**
        *   在返回的每条工单数据对象中，增加一个新字段 `role_type`。
        *   **字段类型:** `Enum`
        *   **枚举值:** `TO_BE_APPROVED`, `ALREADY_APPROVED`, `APPLICANT`, `CC_TO_ME`。
        *   **赋值规则:** 严格按此优先级顺序判断并赋值：待审批 > 已审批 > 申请人 > 抄送人。

### 前端需求

1.  **工单列表页UI变更**
    *   **目标列表:** “进行中”、“已完结”、“我的审批”。
    *   **修改位置:** “单号”列对应的表格单元格内。
    *   **实现逻辑:**
        *   根据每条记录的 `role_type` 字段，通过 **flex 布局** 在单元格的左上角渲染一个带边框的文本标签。
        *   **标签映射关系:**
            *   `APPLICANT` -> `[我申请的]` (默认颜色)
            *   `CC_TO_ME` -> `[抄送我的]` (默认颜色)
            *   `TO_BE_APPROVED` -> `[待我审批]` (橙色)
            *   `ALREADY_APPROVED` -> `[我已审批]` (默认颜色)

### 验收标准
1.  被抄送的工单，会出现在抄送人账号的“进行中”或“已完结”工单列表中。
2.  在各相关列表的“单号”列，身份标签通过Flex布局正确显示在单元格的左上角。
3.  身份标签的显示内容和颜色根据 `role_type` 正确渲染，且优先级符合定义。

---

## 迭代三: 列表筛选与详情页只读权限

### 目标
为工单列表增加按身份角色筛选的功能，并确保被抄送人访问工单详情页时为只读状态。

### 后端需求

1.  **改造API: “进行中”、“已完结”、“我的审批”工单列表接口**
    *   **参数变更:** 接口需支持一个新的查询参数 `filter_by_role`。
        *   **参数类型:** `Array<string>`。
        *   **参数内容:** `role_type` 的枚举字符串数组，例如: `["TO_BE_APPROVED", "CC_TO_ME"]`。
    *   **处理逻辑:** 如果 `filter_by_role` 数组非空，则仅返回 `role_type` 存在于该数组中的工单（使用SQL `IN` 操作符）。如果数组为空 `[]`，则不应用此项筛选。

### 前端需求

1.  **列表页筛选UI与交互**
    *   **实现方式:** 采用 Ant Design Table 组件自带的列筛选功能。
    *   **触发入口:** 在“单号”列的表头标题右侧，增加一个筛选图标。
    *   **交互流程:**
        1.  点击筛选图标，弹出筛选菜单（支持多选）。
        2.  **菜单选项定义:**
            *   **“进行中”/“已完结”列表:** 筛选菜单包含 `[我申请的]`、`[抄送我的]` 两个可勾选的选项。
            *   **“我的审批”列表:** 筛选菜单包含 `[待我审批]`、`[我已审批]`、`[抄送我的]` 三个可勾选的选项。
        3.  菜单下方包含两个按钮：`[Reset]` 和 `[OK]`。
        4.  点击 `[Reset]` 按钮，清除筛选菜单中所有已勾选的选项。
        5.  点击 `[OK]` 按钮，筛选菜单消失，并立即调用列表API刷新数据。API请求中 **总是附带** `filter_by_role` 参数，其值为当前所有已勾选项对应的 `role_type` 字符串数组。若无任何勾选项，则传递一个空数组 `[]`。

2.  **工单详情页权限处理**
    *   **实现逻辑:**
        *   从列表页点击工单时，将该工单的 `role_type` 传递至详情页。
        *   **权限判定:** 确认页面现有的“审批按钮”和“审批人变更按钮”的显示条件（`当前用户是操作员`）对于 `role_type` 为 `CC_TO_ME` 的用户已能正确实现隐藏，无需新增其他权限控制逻辑。

### 验收标准
1.  后端列表接口能够正确处理 `filter_by_role` 数组参数，当数组非空时返回匹配任一角色类型的数据，当数组为空时返回未筛选的数据。
2.  列表的筛选菜单支持多选，按钮文案为 `Reset` 和 `OK`。
3.  点击 `Reset` 按钮会清空筛选菜单中的所有勾选。
4.  点击 `OK` 按钮后，列表会根据当前勾选状态（包括多选、单选、不选）正确刷新。
5.  抄送人 (`role_type` 为 `CC_TO_ME`) 访问工单详情页时，页面上的“审批”和“审批人变更”按钮因现有权限逻辑而不可见。

---

## 迭代四: 消息通知

### 目标
在工单创建成功后，向所有抄送人发送飞书通知，确保信息的及时触达。

### 后端需求

1.  **改造逻辑: 创建工单成功后**
    *   **触发时机:** 在工单数据和 `tb_order_cc` 关系数据均已成功写入数据库之后。
    *   **核心逻辑:**
        1.  遍历本次工单创建时提交的 `cc_user_infos` 数组。
        2.  对于数组中的每一个 `cc_open_id`，调用飞书消息服务，发送通知卡片。
        3.  **通知内容:** 卡片内容与格式必须与发送给该工单申请人的消息卡片 **完全一致**。
        4.  **重要:** 通知卡片 **不包含** 任何指向工单详情页的链接。
    *   **容错处理:**
        *   单条通知发送失败需记录详细错误日志（包含 `order_id` 和失败的 `cc_open_id`）。
        *   单条通知的失败不得中断对后续用户的通知，也不得影响整个创建工单API的成功返回。

### 前端需求
*   无。

### 验收标准
1.  创建一个包含抄送人的工单后，所有被指定的抄送人都能在其飞书客户端收到通知。
2.  抄送人收到的通知卡片，其内容、布局、格式与该工单申请人收到的完全相同。
3.  确认抄送人收到的通知卡片中，不包含任何可点击的链接。
4.  模拟单个抄送人通知发送失败时，创建工单的整体流程不受影响，其他抄送人仍能收到通知，并且后台有明确的错误日志记录。