# 项目迭代二：工单列表整合与角色展示

#### **总览**

本次迭代的核心目标是改造所有工单列表API（“进行中”、“已完结”、“我的审批”），使其能够整合展示当前用户作为“申请人”、“审批人”以及“抄送人”的所有相关工单。我们将严格遵循设计文档中确立的“禁止JOIN，应用层聚合”原则，通过在Service层进行逻辑聚合，并新增必要的DAO层“积木”方法来高效地完成此任务。

#### **最终代码目录结构 (迭代二变更点)**

```
app/ops/bpm/plugins/
└── order/
    ├── orderdto/
    │   └── biz_objects.go            # [改造] 新增RoleType常量和OrderInfoWithRole结构体。
    └── orderbiz/
        ├── order.go                  # [改造] 核心改造点，实现应用层聚合逻辑，重构列表查询方法。
        └── internal/
            └── orderdao/
                ├── ext_order_cc.go   # [改造] 新增GetOrderIDsByCcOpenID方法。
                ├── ext_order.go      # [改造] 新增GetOrdersByIDs方法。
                └── obj.go            # [无变更] 本次迭代不直接修改，但会使用其中定义的结构体。
```

#### **开发流程Mermaid图**

```mermaid
flowchart TD
    subgraph Step 1: 数据结构与常量定义
        A["在 orderdto/biz_objects.go 中定义 RoleType 常量"] --> B["定义 OrderInfoWithRole 内部DTO"];
    end
    subgraph " "
        direction LR
        B -- 可验证 --> V1[验证: 项目可以无错误地编译];
    end

    subgraph Step 2: 实现DAO层积木方法
        C["在 ext_order_cc.go 中新增 GetOrderIDsByCcOpenID"] --> D["在 ext_order.go 中新增 GetOrdersByIDs"];
    end
    subgraph " "
        direction LR
        D -- 可验证 --> V2[验证: 两个新增的DAO方法可通过单元测试验证其功能正确性];
    end
    
    subgraph Step 3: 实现核心聚合逻辑
        E["在 orderbiz/order.go 中实现私有方法 getMyOrders"] --> F["实现 calculateFinalRole 优先级计算逻辑"];
    end
    subgraph " "
        direction LR
        F -- 可验证 --> V3[验证: 核心聚合与计算逻辑可通过单元测试验证];
    end
    
    subgraph Step 4: 重构并集成列表API
        G["重构 GetDoingOrdersByPage"] --> H["重构 GetDoneOrdersByPage"];
        H --> I["重构 GetMyAuditOrderWithSearch"];
    end
    subgraph " "
        direction LR
        I -- 可验证 --> V4[验证: 调用任一列表API, 能正确返回包含抄送工单和role_type的列表];
    end
    
    Step1 --> Step2 --> Step3 --> Step4;
```

---

### **渐进式小步迭代式开发与集成步骤**

#### **第一步：定义数据结构与常量 (DTO Layer)**

此步骤为后续的业务逻辑实现准备好必要的数据容器和类型定义。

1.  **定义角色类型常量和内部DTO**:
    *   **任务**: 打开 `app/ops/bpm/plugins/order/orderdto/biz_objects.go` 文件。
        1.  在文件顶部新增 `RoleType` 常量定义，包含 `TO_BE_APPROVED`, `ALREADY_APPROVED`, `APPLICANT`, `CC_TO_ME` 四个值。
        2.  新增 `OrderInfoWithRole` 内部结构体，用于在Service层临时存储工单及其所有角色。
            ```go
            // RoleType 定义了用户在工单中的角色
            type RoleType string

            const (
                RoleToBeApproved   RoleType = "TO_BE_APPROVED"
                RoleAlreadyApproved RoleType = "ALREADY_APPROVED"
                RoleApplicant       RoleType = "APPLICANT"
                RoleCcToMe          RoleType = "CC_TO_ME"
            )
            
            // OrderInfoWithRole 用于在应用层聚合时，存储一个工单及其所有关联角色
            type OrderInfoWithRole struct {
                Order *orderdao.TbOrder // 复用已有的DAO层结构体
                Roles map[RoleType]bool // 使用map作为set，存储该用户与此工单的所有角色关系
            }
            ```
    *   **验证**: 保存文件后，确保项目可以无错误地编译。

---

#### **第二步：实现DAO层“积木”方法 (DAO Layer)**

此步骤为上层业务逻辑提供获取数据的原子能力，严格遵循“禁止JOIN”原则。

1.  **新增“根据抄送人获取工单ID”方法**:
    *   **任务**: 打开 `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order_cc.go` 文件，新增 `GetOrderIDsByCcOpenID` 方法。
        ```go
        // GetOrderIDsByCcOpenID 根据被抄送人的open_id，查询所有相关的order_id
        func (d *Dao) GetOrderIDsByCcOpenID(ctx context.Context, ccOpenID string) ([]string, error) {
            var orderIDs []string
            if err := d.db.WithContext(ctx).Model(&OrderCc{}).Where("cc_open_id = ?", ccOpenID).Pluck("order_id", &orderIDs).Error; err != nil {
                return nil, err
            }
            return orderIDs, nil
        }
        ```
    *   **验证**: 编写一个单元测试，向 `tb_order_cc` 表插入一些测试数据，然后调用此方法，断言返回的 `orderIDs` 切片是否符合预期。

2.  **新增“根据ID列表批量获取工单”方法**:
    *   **任务**: 打开 `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order.go` 文件，新增 `GetOrdersByIDs` 方法。
        ```go
        // GetOrdersByIDs 根据一组order_id，分页查询对应的工单详情列表
        func (d *Dao) GetOrdersByIDs(ctx context.Context, orderIDs []string, page int, pageSize int) ([]*TbOrder, int64, error) {
            if len(orderIDs) == 0 {
                return []*TbOrder{}, 0, nil
            }
        
            var (
                orders []*TbOrder
                total  int64
                g      errgroup.Group
            )
            
            db := d.db.WithContext(ctx).Model(&TbOrder{}).Where("order_id IN ?", orderIDs)
        
            // 并行执行count和find查询
            g.Go(func() error {
                return db.Count(&total).Error
            })
        
            g.Go(func() error {
                offset := (page - 1) * pageSize
                return db.Offset(offset).Limit(pageSize).Order("id DESC").Find(&orders).Error
            })
        
            if err := g.Wait(); err != nil {
                return nil, 0, err
            }
        
            return orders, total, nil
        }
        ```
    *   **验证**: 编写一个单元测试，向 `tb_order` 表插入测试数据，然后使用一组ID调用此方法，断言返回的工单列表、总数和分页逻辑是否正确。

---

#### **第三步：实现核心聚合逻辑 (Service Layer - Core Logic)**

此步骤在Service层内部构建可复用的聚合能力，是本次迭代的核心。

1.  **实现核心聚合私有方法 `getMyOrders`**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/orderbiz/order.go` 中，新增一个私有的 `getMyOrders` 方法。此方法将执行设计文档中描述的四个步骤：
        1.  并行获取所有角色相关的Order ID（我申请的、我审批的、抄送我的）。
        2.  在内存中聚合ID和角色，存入 `map[string]map[RoleType]bool`。
        3.  调用第二步创建的 `dao.GetOrdersByIDs` 分页查询工单详情。
        4.  将DAO返回的 `[]*TbOrder` 转换为 `[]*orderdto.OrderInfoWithRole`，此时 `Roles` 字段已填充完毕。
    *   **验证**: 为 `getMyOrders` 方法编写一个详尽的单元测试。你需要Mock所有被调用的DAO方法（获取申请的ID、审批的ID、抄送的ID，以及批量获取工单详情），然后验证 `getMyOrders` 的输出（工单列表、角色映射关系、总数）是否完全符合预期。

2.  **实现优先级计算方法 `calculateFinalRole`**:
    *   **任务**: 在 `orderbiz/order.go` 中，新增一个私有的 `calculateFinalRole` 方法，它接收一个 `map[RoleType]bool`，并根据 `待审批 > 已审批 > 申请人 > 抄送人` 的优先级规则返回最终的 `RoleType`。
    *   **验证**: 对 `calculateFinalRole` 进行单元测试，覆盖所有优先级分支。

---

#### **第四步：重构并集成列表API (API Integration)**

此步骤将新构建的聚合能力应用到现有的API接口上，完成最终的整合。

1.  **重构 `GetDoingOrdersByPage` 和 `GetDoneOrdersByPage`**:
    *   **任务**: 修改 `orderbiz/order.go` 中的这两个方法。
        *   清空原有的实现逻辑。
        *   调用 `getMyOrders` 获取聚合后的工单数据。
        *   遍历 `getMyOrders` 的结果，对每个工单调用 `calculateFinalRole` 计算最终角色。
        *   将工单信息和计算出的 `role_type` 组装成最终的 `orderdto.OrderInfo`（或类似结构），返回给Handler。
    *   **验证**: 启动服务。
        1.  调用 `/GetMyDoingOrder` 接口。准备一个用户，他既是某些工单的申请人，又被抄送到另一些工单。验证返回的列表中是否同时包含了这两种工单，并且 `role_type` 字段的值（`APPLICANT`, `CC_TO_ME`）是否正确。
        2.  对 `/GetMyDoneOrder` 接口执行类似的验证。

2.  **重构 `GetMyAuditOrderWithSearch`**:
    *   **任务**: 修改 `orderbiz/order.go` 中的此方法，采用与上一步类似的逻辑进行重构。`GetMyAuditOrderWithSearch` 的特殊之处在于它可能需要处理更多的角色（如 `TO_BE_APPROVED` 和 `ALREADY_APPROVED`）。`getMyOrders` 的设计已经考虑了这一点。
    *   **验证**: 启动服务，调用 `/GetMyAuditOrder` 接口。准备一个用户，他既是某些工单的待审批人，又是另一些工单的已审批人，同时还被抄送到其他工单。验证返回的列表中是否包含了所有这些工单，并且 `role_type` 字段的值是否根据优先级规则被正确计算（例如，一个他待审批的工单，即使他也是申请人，角色也应为 `TO_BE_APPROVED`）。