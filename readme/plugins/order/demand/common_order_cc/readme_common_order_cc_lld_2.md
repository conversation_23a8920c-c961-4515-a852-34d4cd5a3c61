好的，我们已经成功完成了迭代一。现在，我将基于您的需求文档和我们已有的设计，为您提供一份针对**迭代二**的、务实的、完整的详细设计(LLD)。

这份设计将聚焦于实现“工单列表整合展示”功能，同时保持整体架构的完整性，并为迭代三和四预留清晰的扩展点。

---
## **后端详细设计 (LLD) - 迭代二: 工单列表整合展示**

### **与前一版本LLD (迭代一) 的主要区别**

1.  **核心逻辑变更 (Service层)**: 最大的变更是列表查询逻辑。我们将从原有的简单查询，转向**应用层聚合**模式，以实现在不使用数据库`JOIN`的前提下，合并用户作为“申请人”、“审批人”、“抄送人”的工单。
2.  **DAO层扩展**: 为支持应用层聚合，DAO层需要提供新的“积木”式查询方法。我们将在`ext_order_cc.go`中新增根据`cc_open_id`查询`order_id`列表的方法。
3.  **DTO结构扩展**: 在`orderdto/biz_objects.go`中新增`OrderInfoWithRole`结构体，用于在Service层内部封装带有`role_type`的工单信息，并定义`RoleType`常量。
4.  **占位符的实现**: 迭代一中`role_type`等字段是纯粹的接口占位，在迭代二中，我们将**真正地实现`role_type`字段的计算和填充逻辑**。

### 项目结构与总体设计

迭代二的核心是改造所有工单列表API，使其能够展示与当前用户相关的所有工单（包括抄送给我的），并明确标识用户在每个工单中的角色。

我们将严格遵循“禁止JOIN，应用层聚合”的性能策略。这意味着Service层将分别从DAO层获取不同角色的工单ID列表，在内存中进行合并去重，然后一次性查询这些ID对应的工单详情，最后在应用层计算并赋予每个工单正确的`role_type`。

### 目录结构 (迭代二变更点)

```
app/ops/bpm/plugins/
└── order/
    ├── orderdto/
    │   └── biz_objects.go            # [改造] 新增OrderInfoWithRole DTO, 定义RoleType常量
    └── orderbiz/
        ├── order.go                  # [改造] 实现列表接口的应用层聚合逻辑
        └── internal/
            └── orderdao/
                ├── ext_order_cc.go   # [改造] 新增根据cc_open_id查询order_id列表的方法
                └── ext_order.go      # [改造] 新增根据order_id列表查询工单详情的方法
```

### 整体逻辑和交互时序图

下图展示了获取“我的审批”列表的核心流程，体现了应用层聚合的设计。

```mermaid
sequenceDiagram
    participant H as "Handler"
    participant S as "Service (order.go)"
    participant DAO_Approver as "DAO (Approver)"
    participant DAO_Applicant as "DAO (Applicant)"
    participant DAO_CC as "DAO (ext_order_cc.go)"
    participant DAO_Order as "DAO (ext_order.go)"

    H->>S: "GetOrderPageListByOperator(ctx, param)"
    activate S

    Note over S: "步骤1: 并行获取所有相关Order ID"
    S->>DAO_Approver: "获取'待我审批'和'我已审批'的Order ID"
    DAO_Approver-->>S: "approvalOrderIDs"

    S->>DAO_Applicant: "获取'我申请的'Order ID"
    DAO_Applicant-->>S: "applicantOrderIDs"

    S->>DAO_CC: "GetOrderIDsByCcOpenID(ctx, openID)"
    DAO_CC-->>S: "ccOrderIDs"

    Note over S: "步骤2: 在内存中聚合数据"
    S->>S: "创建 orderRoles map[string]map[RoleType]bool"
    S->>S: "合并所有Order ID并去重"
    S->>S: "根据来源填充 orderRoles Map"
    
    Note over S: "步骤3: 分页查询工单详情"
    S->>DAO_Order: "GetOrdersByIDs(ctx, uniqueOrderIDs, page, size)"
    DAO_Order-->>S: "pagedOrders"

    Note over S: "步骤4: 计算并附加RoleType"
    S->>S: "遍历 pagedOrders"
    S->>S: "根据 orderRoles Map 和优先级规则计算最终 role_type"
    S->>S: "将 role_type 附加到每个工单对象"
    
    S-->>H: "返回带 role_type 的工单列表"
    deactivate S
```

### 模块化文件详解 (File-by-File Breakdown)

---
### `app/ops/bpm/plugins/order/orderdto/biz_objects.go`

a. **文件用途说明**: 定义Service层内部及与Handler层交互时使用的数据传输对象(DTO)。

c. **新增/改造内容**

#### **`RoleType` 常量 (新增)**
- **用途:** 定义用户角色类型的枚举值，便于在代码中统一使用。
```go
// RoleType 定义了用户在工单中的角色
type RoleType string

const (
    RoleToBeApproved   RoleType = "TO_BE_APPROVED"
    RoleAlreadyApproved RoleType = "ALREADY_APPROVED"
    RoleApplicant       RoleType = "APPLICANT"
    RoleCcToMe          RoleType = "CC_TO_ME"
)
```

#### **`OrderInfoWithRole` 结构体 (新增)**
- **用途:** 在Service层聚合数据时，临时封装工单信息及其关联的所有角色。
```go
// OrderInfoWithRole 用于在应用层聚合时，存储一个工单及其所有关联角色
type OrderInfoWithRole struct {
    Order *orderdao.Order
    Roles map[RoleType]bool // 使用map作为set，存储该用户与此工单的所有角色关系
}
```

---
### `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order_cc.go`

a. **文件用途说明**: 手动编写的、与`tb_order_cc`表相关的DAO扩展方法。

c. **函数/方法详解**

#### **`GetOrderIDsByCcOpenID` (新增)**
- **用途:** 根据被抄送人的`open_id`，查询所有相关的`order_id`。
- **输入参数:** `ctx context.Context`, `ccOpenID string`
- **输出数据结构:** `[]string`, `error`
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start"] --> B["初始化一个空的 orderIDs 切片"];
      B --> C["SQL: SELECT order_id FROM tb_order_cc WHERE cc_open_id = ?"];
      C --> D["使用 d.db.Model(&OrderCc{}).Where(...).Pluck(order_id, &orderIDs)"];
      D --> E{"检查 err"};
      E -- "err != nil" --> F["返回 nil, err"];
      E -- "err == nil" --> G["返回 orderIDs, nil"];
      F & G --> H["End"];
  ```
---
### `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order.go`

a. **文件用途说明**: 手动编写的、与`tb_order`表相关的复杂DAO查询。

c. **函数/方法详解**

#### **`GetOrdersByIDs` (新增)**
- **用途:** 根据一组`order_id`，分页查询对应的工单详情列表。
- **输入参数:**
    - `ctx context.Context`
    - `orderIDs []string`
    - `page int`
    - `pageSize int`
- **输出数据结构:** `[]*Order`, `int64`, `error` (工单列表, 总数, 错误)
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start"] --> B{"检查 orderIDs 是否为空"};
      B -- "是" --> C["返回空列表, 0, nil"];
      B -- "否" --> D["db := d.db.WithContext(ctx).Model(&Order{}).Where(order_id IN ?, orderIDs)"];
      D --> E["并行执行两个查询"];
      subgraph ParallelExecution
        E1["克隆db查询总数: db.Count(&total)"]
        E2["克隆db并应用分页和排序: db.Offset(...).Limit(...).Order(id DESC).Find(&orders)"]
      end
      E --> F{"任一查询出错?"};
      F -- "是" --> G["返回错误"];
      F -- "否" --> H["返回 orders, total, nil"];
      C & G & H --> I["End"];
  ```
---
### `app/ops/bpm/plugins/order/orderbiz/order.go`

a. **文件用途说明**: 业务逻辑（Service）层，本次迭代将在这里实现核心的应用层聚合逻辑。

c. **函数/方法详解**

#### **`GetDoingOrdersByPage`, `GetDoneOrdersByPage`, `GetOrderPageListByOperator` (改造)**
- **用途:** 对外暴露的列表查询接口，内部逻辑统一调用新的私有聚合方法。
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start"] --> B["从 ctx 中获取当前用户的 open_id"];
      B --> C["调用新的私有方法 getMyOrders(ctx, openID, param)"];
      C --> D["将返回的聚合结果转换为API响应格式"];
      D --> E["End"];
  ```

#### **`getMyOrders` (新增私有方法)**
- **用途:** 实现应用层聚合的核心逻辑。
- **输入参数:** `ctx context.Context`, `openID string`, `param *orderdto.GetMyOrdersParam`
- **输出数据结构:** `[]*orderdto.OrderInfoWithFinalRole`, `int64`, `error`
- **实现流程:**
  ```mermaid
  flowchart TD
      subgraph Step1["1. 并行获取所有角色相关的Order ID"]
        A["调用DAO获取'我申请的' order IDs"]
        B["调用DAO获取'我审批的' order IDs"]
        C["调用dao.GetOrderIDsByCcOpenID获取'抄送我的' order IDs"]
      end
      
      subgraph Step2["2. 内存中聚合ID和角色"]
        D["创建 orderRolesMap: map[string]map[RoleType]bool"]
        E["遍历A, B, C返回的ID列表，填充orderRolesMap"]
        F["从orderRolesMap的键生成唯一的 aLLOrderIDs 列表"]
      end

      subgraph Step3["3. 分页查询工单详情"]
        G["调用dao.GetOrdersByIDs(aLLOrderIDs, param.Page, param.PageSize)"]
      end

      subgraph Step4["4. 计算最终角色并组装结果"]
        H["遍历返回的工单列表"]
        I["对每个工单，从orderRolesMap中获取其所有角色"]
        J["调用私有方法 calculateFinalRole(roles) 计算最终角色"]
        K["将工单和最终角色组装成新对象，添加到结果列表"]
      end
      
      Step1 --> Step2 --> Step3 --> Step4
  ```

#### **`calculateFinalRole` (新增私有方法)**
- **用途:** 根据优先级规则，从一个工单的多个角色中计算出最终应该展示的角色。
- **输入参数:** `roles map[RoleType]bool`
- **输出数据结构:** `RoleType`
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start"] --> B{"roles 中包含 TO_BE_APPROVED?"};
      B -- "是" --> B_Yes["返回 TO_BE_APPROVED"];
      B -- "否" --> C{"roles 中包含 ALREADY_APPROVED?"};
      C -- "是" --> C_Yes["返回 ALREADY_APPROVED"];
      C -- "否" --> D{"roles 中包含 APPLICANT?"};
      D -- "是" --> D_Yes["返回 APPLICANT"];
      D -- "否" --> E["返回 CC_TO_ME"];
      B_Yes & C_Yes & D_Yes & E --> F["End"];
  ```

## 迭代演进依据
这份详细设计确保了系统能够平滑地进行后续迭代：
1.  **性能与规范的平衡**: 严格遵守了“禁止JOIN，应用层聚合”的原则。虽然增加了应用层的复杂性，但通过并行查询和一次性批量获取详情，保证了数据库交互的高效性，避免了慢查询风险。
2.  **模块化和可复用性**: 新增的DAO方法（如`GetOrdersByIDs`）是通用的，未来可以在其他需要批量查询工单的场景中复用。`calculateFinalRole`等内部逻辑也高度内聚，易于理解和维护。
3.  **为迭代三准备就绪**: `getMyOrders`方法的参数`param *orderdto.GetMyOrdersParam`已经包含了`FilterByRole`字段。在迭代三中，我们只需在此方法内部添加一步逻辑：在返回最终结果前，根据`param.FilterByRole`对已计算出最终角色的工单列表进行一次内存过滤即可，无需改动任何DAO或核心聚合逻辑。
4.  **清晰的占位**: 迭代三（列表筛选）和迭代四（消息通知）的功能点已在代码结构和函数签名中预留了清晰的扩展点，后续开发任务明确。