# 后端详细设计 (LLD) - 迭代二: 工单列表整合展示

### **与前一版本LLD (迭代一) 的主要区别**

1.  **核心逻辑变更 (Service层)**: 最大的变更是列表查询逻辑。我们采用**页面独立查询**模式，每个页面都有专属的查询方法，使用连表查询直接获取工单及角色信息。
2.  **DAO层扩展**: 为每个页面创建独立的连表查询方法。进行中工单和已完结工单使用新的连表查询，我的审批工单保持原有逻辑。
3.  **DTO结构扩展**: 在`orderdto/biz_objects.go`中新增`OrderInfoWithRole`结构体和`RoleType`常量，在`orderdao/obj.go`中新增`OrderWithRoleInfo`结构体用于连表查询结果。
4.  **查询优化**: 使用`cc_user_email`替代`cc_open_id`进行抄送查询，避免额外的用户信息获取步骤。

### 项目结构与总体设计

迭代二的核心是改造工单列表API，使其能够展示与当前用户相关的所有工单（包括抄送给我的），并明确标识用户在每个工单中的角色。

我们采用**页面独立查询**策略：
- **我的审批工单**: 保持原有复杂搜索逻辑不变
- **进行中工单**: 使用独立的连表查询方法，查询条件为`result = 0`
- **已完结工单**: 使用独立的连表查询方法，查询条件为`result = 1`

### 目录结构 (迭代二变更点)

```
app/ops/bpm/plugins/
└── order/
    ├── orderdto/
    │   └── biz_objects.go            # [改造] 新增RoleType常量、OrderInfoWithRole结构体、参数结构体
    └── orderbiz/
        ├── order.go                  # [改造] 重构三个页面的查询方法，使用结构化参数
        └── internal/
            └── orderdao/
                ├── obj.go             # [改造] 新增OrderWithRoleInfo结构体
                └── ext_order.go       # [改造] 新增独立的连表查询方法
```

### 整体逻辑和交互时序图

下图展示了获取"进行中工单"列表的核心流程，体现了独立连表查询的设计。

```mermaid
sequenceDiagram
    participant H as "Handler"
    participant S as "Service (order.go)"
    participant DAO as "DAO (ext_order.go)"

    H->>S: "GetDoingOrdersByPage(ctx, param)"
    activate S

    Note over S: "使用独立的连表查询方法"
    S->>DAO: "GetMyDoingOrdersWithRole(ctx, userEmail, page, pageSize)"
    
    Note over DAO: "执行连表查询"
    DAO->>DAO: "LEFT JOIN tb_stage, tb_order_cc"
    DAO->>DAO: "WHERE result = 0 AND (申请人 OR 审批人 OR 抄送人)"
    DAO->>DAO: "在SQL层面计算角色标识"
    DAO-->>S: "ordersWithRole"

    Note over S: "转换结果并计算最终角色"
    S->>S: "构建角色映射 map[RoleType]bool"
    S->>S: "调用 calculateFinalRole 计算优先级"
    S->>S: "组装 OrderInfo 结果"
    
    S-->>H: "返回带 role_type 的工单列表"
    deactivate S
```

### 模块化文件详解 (File-by-File Breakdown)

---
### `app/ops/bpm/plugins/order/orderdto/biz_objects.go`

a. **文件用途说明**: 定义Service层内部及与Handler层交互时使用的数据传输对象(DTO)。

c. **新增/改造内容**

#### **`RoleType` 常量 (新增)**
- **用途:** 定义用户角色类型的枚举值，便于在代码中统一使用。
```go
// RoleType 定义了用户在工单中的角色
type RoleType string

const (
    RoleToBeApproved   RoleType = "TO_BE_APPROVED"
    RoleAlreadyApproved RoleType = "ALREADY_APPROVED"
    RoleApplicant       RoleType = "APPLICANT"
    RoleCcToMe          RoleType = "CC_TO_ME"
)
```

#### **参数结构体 (新增)**
- **用途:** 统一参数传递格式，遵循分层架构设计。
```go
// MyDoingOrderSearchParam 我的进行中工单搜索参数
type MyDoingOrderSearchParam struct {
    PageNum      int      `json:"page_num"`
    PageSize     int      `json:"page_size"`
    UserEmail    string   `json:"user_email"`
    FilterByRole []string `json:"filter_by_role"` // 迭代三预留
}

// MyDoneOrderSearchParam 我的已完结工单搜索参数
type MyDoneOrderSearchParam struct {
    PageNum      int      `json:"page_num"`
    PageSize     int      `json:"page_size"`
    UserEmail    string   `json:"user_email"`
    FilterByRole []string `json:"filter_by_role"` // 迭代三预留
}
```

#### **`OrderInfoWithRole` 结构体 (新增)**
- **用途:** 在Service层聚合数据时，临时封装工单信息及其关联的所有角色。
```go
// OrderInfoWithRole 用于在应用层聚合时，存储一个工单及其所有关联角色
type OrderInfoWithRole struct {
    Order interface{}       // 复用已有的DAO层结构体，使用interface{}避免循环导入
    Roles map[RoleType]bool // 使用map作为set，存储该用户与此工单的所有角色关系
}
```

---
### `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/obj.go`

a. **文件用途说明**: 定义DAO层内部使用的数据结构。

c. **新增内容**

#### **`OrderWithRoleInfo` 结构体 (新增)**
- **用途:** 连表查询结果结构体，包含工单信息和角色信息。
```go
// OrderWithRoleInfo 连表查询结果结构体，包含工单信息和角色信息
type OrderWithRoleInfo struct {
    // 工单基本信息
    Id            int       `db:"id"`
    OrderId       string    `db:"order_id"`
    OrderType     string    `db:"order_type"`
    Info          string    `db:"info"`
    Exigency      int       `db:"exigency"`
    ApplyMsg      string    `db:"apply_msg"`
    ProposerEmail string    `db:"proposer_email"`
    OpsOwnerEmail string    `db:"ops_owner_email"`
    TotalStageNum int       `db:"total_stage_num"`
    CurrentStage  int       `db:"current_stage"`
    Result        int       `db:"result"`
    ResultMsg     string    `db:"result_msg"`
    IsDel         int       `db:"is_del"`
    Ctime         time.Time `db:"ctime"`
    Mtime         time.Time `db:"mtime"`
    
    // 角色信息
    IsApplicant       bool `db:"is_applicant"`        // 是否为申请人
    IsToBeApproved    bool `db:"is_to_be_approved"`   // 是否为待审批人
    IsAlreadyApproved bool `db:"is_already_approved"` // 是否为已审批人
    IsCcToMe          bool `db:"is_cc_to_me"`         // 是否为抄送人
}
```

---
### `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order.go`

a. **文件用途说明**: 手动编写的、与`tb_order`表相关的复杂DAO查询。

c. **函数/方法详解**

#### **`GetMyDoingOrdersWithRole` (新增)**
- **用途:** 获取用户的进行中工单及角色信息。
- **输入参数:** `ctx context.Context`, `userEmail string`, `page int`, `pageSize int`
- **输出数据结构:** `[]*OrderWithRoleInfo`, `int64`, `error`
- **核心SQL:**
```sql
SELECT DISTINCT
    o.id, o.order_id, o.order_type, ...,
    -- 角色标识在SQL层面计算
    CASE WHEN o.proposer_email = ? THEN 1 ELSE 0 END as is_applicant,
    CASE WHEN s.operator = ? AND s.result = 0 THEN 1 ELSE 0 END as is_to_be_approved,
    CASE WHEN s.operator = ? AND s.result = 1 THEN 1 ELSE 0 END as is_already_approved,
    CASE WHEN cc.cc_user_email = ? THEN 1 ELSE 0 END as is_cc_to_me
FROM tb_order o
LEFT JOIN tb_stage s ON o.order_id = s.order_id
LEFT JOIN tb_order_cc cc ON o.order_id = cc.order_id
WHERE o.is_del = 0 AND o.result = 0
AND (
    o.proposer_email = ?           -- 我申请的
    OR s.operator = ?              -- 我审批的
    OR cc.cc_user_email = ?        -- 抄送我的
)
```

#### **`GetMyDoneOrdersWithRole` (新增)**
- **用途:** 获取用户的已完结工单及角色信息。
- **输入参数:** `ctx context.Context`, `userEmail string`, `page int`, `pageSize int`
- **输出数据结构:** `[]*OrderWithRoleInfo`, `int64`, `error`
- **核心SQL:** 与`GetMyDoingOrdersWithRole`类似，但查询条件为`o.result = 1`

---
### `app/ops/bpm/plugins/order/orderbiz/order.go`

a. **文件用途说明**: 业务逻辑（Service）层，本次迭代将在这里实现独立的页面查询逻辑。

c. **函数/方法详解**

#### **`GetDoingOrdersByPage`, `GetDoneOrdersByPage` (改造)**
- **用途:** 对外暴露的列表查询接口，使用独立的连表查询方法。
- **参数变更:** 使用结构化参数 `*orderdto.MyDoingOrderSearchParam` 和 `*orderdto.MyDoneOrderSearchParam`
- **实现流程:**
```mermaid
flowchart TD
    A["Start"] --> B["调用独立的连表查询方法"];
    B --> C["GetMyDoingOrdersWithRole 或 GetMyDoneOrdersWithRole"];
    C --> D["构建角色映射"];
    D --> E["调用 calculateFinalRole 计算最终角色"];
    E --> F["组装 OrderInfo 结果"];
    F --> G["End"];
```

#### **`GetMyAuditOrderWithSearch` (保持不变)**
- **用途:** 保持原有的复杂搜索逻辑不变。
- **实现:** 直接调用 `biz.Dao.SelectMyAuditOrders(ctx, daoParam)`

#### **`calculateFinalRole` (新增私有方法)**
- **用途:** 根据优先级规则，从一个工单的多个角色中计算出最终应该展示的角色。
- **输入参数:** `roles map[RoleType]bool`
- **输出数据结构:** `RoleType`
- **优先级规则:** 待审批 > 已审批 > 申请人 > 抄送人

## 迭代演进依据

这份详细设计确保了系统能够平滑地进行后续迭代：

1.  **性能与规范的平衡**: 使用连表查询提高性能，同时保持代码的清晰性和可维护性。
2.  **模块化和解耦合**: 每个页面都有独立的查询方法，修改一个页面不会影响其他页面。
3.  **为迭代三准备就绪**: 参数结构体中已预留`FilterByRole`字段，为角色筛选功能做好准备。
4.  **清晰的职责分离**: 我的审批工单保持复杂搜索功能，进行中和已完结工单专注于角色聚合。
