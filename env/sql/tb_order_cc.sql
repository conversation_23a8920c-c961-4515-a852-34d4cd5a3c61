CREATE TABLE `tb_order_cc` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `order_id` varchar(16) NOT NULL COMMENT '工单ID',
  `cc_open_id` varchar(64) NOT NULL COMMENT '被抄送人飞书Open ID',
  `cc_email` varchar(32) NOT NULL COMMENT '被抄送人邮箱',
  `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_cc` (`order_id`,`cc_open_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单抄送关系表';
